const express = require('express');
const router = express.Router();
const { query } = require('../models/database');
const { sanitizeInput } = require('../middleware/validation');

// Middleware
router.use(sanitizeInput);

// GET - Ambil semua jenis pemeriksaan yang aktif
router.get('/', async (req, res) => {
    try {
        const examinations = await query(
            `SELECT id, code, name, description, price 
             FROM examination_types 
             WHERE is_active = TRUE 
             ORDER BY name ASC`
        );
        
        res.json({ examinations });
        
    } catch (error) {
        console.error('Get examinations error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil data jenis pemeriksaan' 
        });
    }
});

// GET - Ambil jenis pemeriksaan berdasarkan kategori
router.get('/category/:category', async (req, res) => {
    try {
        const { category } = req.params;
        
        // Mapping kategori ke kode pemeriksaan
        const categoryMapping = {
            'darah': ['LAB001', 'LAB003', 'LAB004', 'LAB005', 'LAB008', 'LAB009'],
            'urine': ['LAB002'],
            'fungsi-organ': ['LAB006', 'LAB007'],
            'infeksi': ['LAB010']
        };
        
        if (!categoryMapping[category]) {
            return res.status(400).json({ 
                error: 'Kategori tidak valid' 
            });
        }
        
        const examinations = await query(
            `SELECT id, code, name, description, price 
             FROM examination_types 
             WHERE code IN (?) AND is_active = TRUE 
             ORDER BY name ASC`,
            [categoryMapping[category]]
        );
        
        res.json({ 
            category,
            examinations 
        });
        
    } catch (error) {
        console.error('Get examinations by category error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil data pemeriksaan berdasarkan kategori' 
        });
    }
});

// GET - Ambil detail jenis pemeriksaan berdasarkan ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!/^\d+$/.test(id)) {
            return res.status(400).json({ 
                error: 'ID pemeriksaan tidak valid' 
            });
        }
        
        const examination = await query(
            `SELECT id, code, name, description, price, is_active 
             FROM examination_types 
             WHERE id = ?`,
            [id]
        );
        
        if (examination.length === 0) {
            return res.status(404).json({ 
                error: 'Jenis pemeriksaan tidak ditemukan' 
            });
        }
        
        res.json({ examination: examination[0] });
        
    } catch (error) {
        console.error('Get examination detail error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil detail pemeriksaan' 
        });
    }
});

// GET - Cari jenis pemeriksaan berdasarkan nama
router.get('/search/:query', async (req, res) => {
    try {
        const { query: searchQuery } = req.params;
        
        if (searchQuery.length < 2) {
            return res.status(400).json({ 
                error: 'Query pencarian minimal 2 karakter' 
            });
        }
        
        const examinations = await query(
            `SELECT id, code, name, description, price 
             FROM examination_types 
             WHERE (name LIKE ? OR description LIKE ?) AND is_active = TRUE 
             ORDER BY name ASC 
             LIMIT 20`,
            [`%${searchQuery}%`, `%${searchQuery}%`]
        );
        
        res.json({ 
            query: searchQuery,
            examinations 
        });
        
    } catch (error) {
        console.error('Search examinations error:', error);
        res.status(500).json({ 
            error: 'Gagal mencari jenis pemeriksaan' 
        });
    }
});

// GET - Ambil paket pemeriksaan populer
router.get('/packages/popular', async (req, res) => {
    try {
        // Paket pemeriksaan yang sering dipilih bersamaan
        const packages = [
            {
                id: 'basic-checkup',
                name: 'Paket Pemeriksaan Dasar',
                description: 'Pemeriksaan dasar untuk kesehatan umum',
                examinations: [1, 2, 3], // Darah Lengkap, Urine Lengkap, Gula Darah Puasa
                discount: 10
            },
            {
                id: 'diabetes-checkup',
                name: 'Paket Pemeriksaan Diabetes',
                description: 'Pemeriksaan khusus untuk monitoring diabetes',
                examinations: [3, 8], // Gula Darah Puasa, HbA1c
                discount: 5
            },
            {
                id: 'lipid-profile',
                name: 'Paket Profil Lemak',
                description: 'Pemeriksaan lengkap profil lemak darah',
                examinations: [4, 9], // Kolesterol Total, Profil Lipid
                discount: 15
            },
            {
                id: 'liver-kidney',
                name: 'Paket Fungsi Hati & Ginjal',
                description: 'Pemeriksaan fungsi organ vital',
                examinations: [6, 7], // Fungsi Hati, Fungsi Ginjal
                discount: 12
            }
        ];
        
        // Ambil detail pemeriksaan untuk setiap paket
        for (let package of packages) {
            const examDetails = await query(
                `SELECT id, name, price FROM examination_types 
                 WHERE id IN (?) AND is_active = TRUE`,
                [package.examinations]
            );
            
            const totalPrice = examDetails.reduce((sum, exam) => sum + parseFloat(exam.price), 0);
            const discountAmount = totalPrice * (package.discount / 100);
            
            package.examination_details = examDetails;
            package.original_price = totalPrice;
            package.discounted_price = totalPrice - discountAmount;
            package.savings = discountAmount;
        }
        
        res.json({ packages });
        
    } catch (error) {
        console.error('Get popular packages error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil paket pemeriksaan populer' 
        });
    }
});

// GET - Statistik jenis pemeriksaan
router.get('/stats/popular', async (req, res) => {
    try {
        const stats = await query(
            `SELECT et.id, et.name, et.price, COUNT(re.id) as total_orders,
                    AVG(re.price) as avg_price
             FROM examination_types et
             LEFT JOIN registration_examinations re ON et.id = re.examination_type_id
             LEFT JOIN registrations r ON re.registration_id = r.id
             WHERE et.is_active = TRUE 
             AND (r.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) OR r.created_at IS NULL)
             GROUP BY et.id, et.name, et.price
             ORDER BY total_orders DESC, et.name ASC
             LIMIT 10`
        );
        
        res.json({ 
            period: '30 hari terakhir',
            popular_examinations: stats 
        });
        
    } catch (error) {
        console.error('Get examination stats error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil statistik pemeriksaan' 
        });
    }
});

module.exports = router;
