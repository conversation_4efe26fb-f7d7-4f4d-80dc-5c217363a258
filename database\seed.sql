-- Seed data untuk testing sistem

USE lab_registration;

-- Update password admin default (password: admin123)
UPDATE admins SET password = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE username = 'admin';

-- Insert sample patients
INSERT INTO patients (nik, full_name, birth_date, gender, phone, email, address, bpjs_number, emergency_contact_name, emergency_contact_phone) VALUES
('1234567890123456', '<PERSON>', '1990-01-15', 'L', '081234567890', '<EMAIL>', 'Jl. Contoh No. 123, Jakarta Selatan', '1234567890123', '<PERSON>', '081234567891'),
('2345678901234567', '<PERSON>', '1985-05-20', 'P', '081234567892', '<EMAIL>', '<PERSON><PERSON>. <PERSON> No. 456, Jakarta Utara', '2345678901234', '<PERSON>', '081234567893'),
('3456789012345678', '<PERSON>', '1992-03-10', 'L', '081234567894', '<EMAIL>', 'Jl. Test No. 789, Jakarta Barat', NULL, 'Siti <PERSON>', '081234567895'),
('4567890123456789', 'Siti Nur<PERSON>za', '1988-07-25', 'P', '081234567896', '<EMAIL>', 'Jl. Demo No. 321, Jakarta Timur', '4567890123456', 'Ahmad Nurhaliza', '081234567897'),
('5678901234567890', 'Budi Santoso', '1995-12-05', 'L', '081234567898', '<EMAIL>', 'Jl. Example No. 654, Jakarta Pusat', '5678901234567', 'Ani Santoso', '081234567899');

-- Insert sample registrations
INSERT INTO registrations (registration_number, patient_id, examination_date, examination_time, status, notes, total_price, payment_status) VALUES
('REG2024010001', 1, '2024-01-15', '08:00', 'completed', 'Pemeriksaan rutin', 150000, 'paid'),
('REG2024010002', 2, '2024-01-16', '09:30', 'confirmed', NULL, 85000, 'unpaid'),
('REG2024010003', 3, '2024-01-17', '10:00', 'pending', 'Perlu konfirmasi jadwal', 120000, 'unpaid'),
('REG2024010004', 4, '2024-01-18', '11:30', 'completed', NULL, 200000, 'paid'),
('REG2024010005', 5, '2024-01-19', '14:00', 'confirmed', 'Pasien diabetes', 160000, 'unpaid');

-- Insert sample registration examinations
INSERT INTO registration_examinations (registration_id, examination_type_id, price) VALUES
-- Registration 1: Darah Lengkap + Urine Lengkap + Gula Darah
(1, 1, 75000),
(1, 2, 50000),
(1, 3, 35000),

-- Registration 2: Kolesterol + Asam Urat
(2, 4, 40000),
(2, 5, 35000),

-- Registration 3: HbA1c + Gula Darah
(3, 8, 120000),
(3, 3, 35000),

-- Registration 4: Profil Lipid + Fungsi Hati
(4, 9, 85000),
(4, 6, 80000),

-- Registration 5: Darah Lengkap + Fungsi Ginjal + HbA1c
(5, 1, 75000),
(5, 7, 70000),
(5, 8, 120000);

-- Insert sample documents
INSERT INTO documents (registration_id, document_type, original_name, file_name, file_path, file_size, mime_type) VALUES
(1, 'ktp', 'KTP_John_Doe.jpg', 'doc-1234567890-KTP_John_Doe.jpg', '/uploads/documents/doc-1234567890-KTP_John_Doe.jpg', 245760, 'image/jpeg'),
(1, 'bpjs', 'BPJS_John_Doe.pdf', 'doc-1234567891-BPJS_John_Doe.pdf', '/uploads/documents/doc-1234567891-BPJS_John_Doe.pdf', 512000, 'application/pdf'),
(2, 'ktp', 'KTP_Jane_Smith.jpg', 'doc-1234567892-KTP_Jane_Smith.jpg', '/uploads/documents/doc-1234567892-KTP_Jane_Smith.jpg', 198432, 'image/jpeg'),
(3, 'rujukan', 'Rujukan_Ahmad.pdf', 'doc-1234567893-Rujukan_Ahmad.pdf', '/uploads/documents/doc-1234567893-Rujukan_Ahmad.pdf', 387654, 'application/pdf');

-- Insert admin activity logs
INSERT INTO admin_logs (admin_id, action, ip_address, user_agent) VALUES
(1, 'login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, 'update_registration_status', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, 'view_dashboard', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, 'export_report', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

-- Update examination types dengan data yang lebih realistis
UPDATE examination_types SET 
    description = 'Pemeriksaan lengkap darah meliputi Hemoglobin, Hematokrit, Leukosit, Trombosit, LED, dan hitung jenis leukosit'
WHERE code = 'LAB001';

UPDATE examination_types SET 
    description = 'Pemeriksaan urine lengkap meliputi warna, kejernihan, berat jenis, protein, glukosa, keton, bilirubin, urobilinogen, nitrit, leukosit esterase, dan sedimen'
WHERE code = 'LAB002';

UPDATE examination_types SET 
    description = 'Pemeriksaan kadar glukosa darah setelah puasa 8-12 jam untuk deteksi diabetes mellitus'
WHERE code = 'LAB003';

UPDATE examination_types SET 
    description = 'Pemeriksaan kadar kolesterol total dalam darah untuk evaluasi risiko penyakit jantung'
WHERE code = 'LAB004';

UPDATE examination_types SET 
    description = 'Pemeriksaan kadar asam urat dalam darah untuk deteksi hiperurisemia dan gout'
WHERE code = 'LAB005';

UPDATE examination_types SET 
    description = 'Pemeriksaan SGOT (AST) dan SGPT (ALT) untuk evaluasi fungsi hati'
WHERE code = 'LAB006';

UPDATE examination_types SET 
    description = 'Pemeriksaan ureum dan kreatinin untuk evaluasi fungsi ginjal'
WHERE code = 'LAB007';

UPDATE examination_types SET 
    description = 'Pemeriksaan HbA1c untuk monitoring kontrol gula darah jangka panjang (2-3 bulan)'
WHERE code = 'LAB008';

UPDATE examination_types SET 
    description = 'Pemeriksaan profil lipid lengkap meliputi kolesterol total, HDL, LDL, dan trigliserida'
WHERE code = 'LAB009';

UPDATE examination_types SET 
    description = 'Pemeriksaan HBsAg untuk deteksi infeksi Hepatitis B'
WHERE code = 'LAB010';

-- Insert additional examination types
INSERT INTO examination_types (code, name, description, price, is_active) VALUES
('LAB011', 'Widal Test', 'Pemeriksaan untuk deteksi demam tifoid (typhus)', 45000, TRUE),
('LAB012', 'Anti HIV', 'Pemeriksaan untuk deteksi antibodi HIV', 85000, TRUE),
('LAB013', 'VDRL/RPR', 'Pemeriksaan untuk deteksi sifilis', 55000, TRUE),
('LAB014', 'Golongan Darah ABO/Rh', 'Pemeriksaan penentuan golongan darah dan rhesus', 25000, TRUE),
('LAB015', 'Tes Kehamilan (Beta HCG)', 'Pemeriksaan untuk deteksi kehamilan', 35000, TRUE),
('LAB016', 'Elektrolit (Na, K, Cl)', 'Pemeriksaan kadar elektrolit dalam darah', 95000, TRUE),
('LAB017', 'Protein Total & Albumin', 'Pemeriksaan kadar protein total dan albumin', 65000, TRUE),
('LAB018', 'Bilirubin Total & Direk', 'Pemeriksaan kadar bilirubin untuk evaluasi fungsi hati', 55000, TRUE),
('LAB019', 'Feses Lengkap', 'Pemeriksaan feses meliputi makroskopis, mikroskopis, dan parasit', 40000, TRUE),
('LAB020', 'Kultur Urine', 'Pemeriksaan kultur dan sensitivitas bakteri dalam urine', 125000, TRUE);

-- Insert sample data untuk bulan sebelumnya (untuk testing laporan)
INSERT INTO registrations (registration_number, patient_id, examination_date, examination_time, status, notes, total_price, payment_status, created_at) VALUES
('REG2023120001', 1, '2023-12-15', '08:00', 'completed', 'Pemeriksaan rutin bulanan', 175000, 'paid', '2023-12-10 08:30:00'),
('REG2023120002', 2, '2023-12-16', '09:30', 'completed', NULL, 95000, 'paid', '2023-12-11 10:15:00'),
('REG2023120003', 3, '2023-12-17', '10:00', 'completed', 'Follow up diabetes', 140000, 'paid', '2023-12-12 14:20:00'),
('REG2023120004', 4, '2023-12-18', '11:30', 'completed', NULL, 220000, 'paid', '2023-12-13 16:45:00'),
('REG2023120005', 5, '2023-12-19', '14:00', 'completed', 'Pemeriksaan komprehensif', 180000, 'paid', '2023-12-14 09:10:00');

-- Insert examination details untuk registrasi bulan lalu
INSERT INTO registration_examinations (registration_id, examination_type_id, price) VALUES
-- Registration 6: Darah Lengkap + Urine + Gula Darah + Kolesterol
(6, 1, 75000),
(6, 2, 50000),
(6, 3, 35000),
(6, 4, 40000),

-- Registration 7: Profil Lipid + Asam Urat
(7, 9, 85000),
(7, 5, 35000),

-- Registration 8: HbA1c + Fungsi Hati
(8, 8, 120000),
(8, 6, 80000),

-- Registration 9: Darah Lengkap + Fungsi Ginjal + Profil Lipid
(9, 1, 75000),
(9, 7, 70000),
(9, 9, 85000),

-- Registration 10: Pemeriksaan komprehensif
(10, 1, 75000),
(10, 2, 50000),
(10, 8, 120000),
(10, 9, 85000);

COMMIT;
