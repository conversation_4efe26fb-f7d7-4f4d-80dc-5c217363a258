const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

// Konfigurasi storage untuk dokumen
const documentStorage = multer.diskStorage({
    destination: async (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../../uploads/documents');
        try {
            await fs.mkdir(uploadDir, { recursive: true });
            cb(null, uploadDir);
        } catch (error) {
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
        cb(null, `${Date.now()}-${sanitizedName}`);
    }
});

// File filter untuk dokumen
const documentFileFilter = (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
        return cb(null, true);
    } else {
        cb(new Error('Hanya file gambar (JPG, PNG) dan dokumen (PDF, DOC, DOCX) yang diizinkan'));
    }
};

// Multer configuration untuk dokumen
const uploadDocuments = multer({
    storage: documentStorage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB per file
        files: 5 // Maksimal 5 file
    },
    fileFilter: documentFileFilter
});

// Middleware untuk handle upload errors
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                error: 'File terlalu besar. Maksimal 5MB per file.'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                error: 'Terlalu banyak file. Maksimal 5 file.'
            });
        }
        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                error: 'Field file tidak dikenali.'
            });
        }
    }
    
    if (error.message.includes('diizinkan')) {
        return res.status(400).json({
            error: error.message
        });
    }
    
    next(error);
};

// Middleware untuk validasi file yang diupload
const validateUploadedFiles = (req, res, next) => {
    if (!req.files || req.files.length === 0) {
        return res.status(400).json({
            error: 'Tidak ada file yang diupload'
        });
    }
    
    // Validasi tambahan untuk setiap file
    for (const file of req.files) {
        // Cek ukuran file
        if (file.size > 5 * 1024 * 1024) {
            return res.status(400).json({
                error: `File ${file.originalname} terlalu besar. Maksimal 5MB.`
            });
        }
        
        // Cek ekstensi file
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx'];
        const fileExtension = path.extname(file.originalname).toLowerCase();
        
        if (!allowedExtensions.includes(fileExtension)) {
            return res.status(400).json({
                error: `File ${file.originalname} memiliki ekstensi yang tidak diizinkan.`
            });
        }
    }
    
    next();
};

// Middleware untuk cleanup file jika terjadi error
const cleanupOnError = (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
        // Jika response error dan ada file yang diupload, hapus file
        if (res.statusCode >= 400 && req.files) {
            req.files.forEach(async (file) => {
                try {
                    await fs.unlink(file.path);
                } catch (unlinkError) {
                    console.error('Failed to cleanup uploaded file:', unlinkError);
                }
            });
        }
        
        originalSend.call(this, data);
    };
    
    next();
};

// Fungsi untuk membuat direktori upload jika belum ada
async function ensureUploadDirectories() {
    const directories = [
        path.join(__dirname, '../../uploads'),
        path.join(__dirname, '../../uploads/documents'),
        path.join(__dirname, '../../uploads/temp')
    ];
    
    for (const dir of directories) {
        try {
            await fs.mkdir(dir, { recursive: true });
        } catch (error) {
            console.error(`Failed to create directory ${dir}:`, error);
        }
    }
}

// Fungsi untuk membersihkan file temporary yang lama
async function cleanupOldTempFiles() {
    try {
        const tempDir = path.join(__dirname, '../../uploads/temp');
        const files = await fs.readdir(tempDir);
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 jam
        
        for (const file of files) {
            const filePath = path.join(tempDir, file);
            const stats = await fs.stat(filePath);
            
            if (now - stats.mtime.getTime() > maxAge) {
                await fs.unlink(filePath);
                console.log(`Cleaned up old temp file: ${file}`);
            }
        }
    } catch (error) {
        console.error('Error cleaning up temp files:', error);
    }
}

// Fungsi untuk validasi tipe dokumen berdasarkan registrasi
const validateDocumentType = (req, res, next) => {
    const allowedDocumentTypes = ['ktp', 'bpjs', 'rujukan', 'other'];
    const documentType = req.body.document_type;
    
    if (documentType && !allowedDocumentTypes.includes(documentType)) {
        return res.status(400).json({
            error: 'Tipe dokumen tidak valid. Pilih: ktp, bpjs, rujukan, atau other'
        });
    }
    
    next();
};

// Inisialisasi direktori saat module dimuat
ensureUploadDirectories();

// Cleanup file lama setiap 6 jam
setInterval(cleanupOldTempFiles, 6 * 60 * 60 * 1000);

module.exports = {
    uploadDocuments,
    handleUploadError,
    validateUploadedFiles,
    cleanupOnError,
    validateDocumentType,
    ensureUploadDirectories,
    cleanupOldTempFiles
};
