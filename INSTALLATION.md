# Panduan Instalasi Sistem Pendaftaran Online Laboratorium

## 📋 Persyaratan Sistem

### Software yang <PERSON>:
- **Node.js** (v14.0.0 atau lebih baru)
- **MySQL** (v8.0 atau lebih baru)
- **XAMPP** (untuk development lokal)
- **Git** (untuk version control)

### Browser yang Didukung:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Langkah Instalasi

### 1. Persiapan Environment

#### Install Node.js
1. Download Node.js dari [nodejs.org](https://nodejs.org/)
2. Install dengan mengikuti wizard instalasi
3. Verifikasi instalasi:
   ```bash
   node --version
   npm --version
   ```

#### Install XAMPP
1. Download XAMPP dari [apachefriends.org](https://www.apachefriends.org/)
2. Install dan jalankan Apache + MySQL
3. Akses phpMyAdmin di `http://localhost/phpmyadmin`

### 2. Setup Database

#### Buat Database
1. Buka phpMyAdmin
2. Buat database baru dengan nama `lab_registration`
3. Import schema database:
   ```sql
   -- Jalankan file database/schema.sql
   ```
4. (Opsional) Import data sample:
   ```sql
   -- Jalankan file database/seed.sql
   ```

#### Konfigurasi Database
1. Copy file `.env.example` menjadi `.env`
2. Edit konfigurasi database:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=
   DB_NAME=lab_registration
   ```

### 3. Install Dependencies

```bash
# Masuk ke direktori proyek
cd "PERMINTAAN PEMERIKSAAN PENDAFTARAN ONLINE LABORATORIUM"

# Install dependencies
npm install
```

### 4. Konfigurasi Environment

Edit file `.env` sesuai kebutuhan:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=lab_registration

# JWT Secret (ganti dengan string random yang aman)
JWT_SECRET=your_very_secure_jwt_secret_key_here

# Email Configuration (opsional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Server Configuration
PORT=3000
NODE_ENV=development

# BPJS API Configuration (opsional)
BPJS_API_URL=https://apijkn.bpjs-kesehatan.go.id
BPJS_CONS_ID=your_consumer_id
BPJS_SECRET_KEY=your_secret_key
```

### 5. Jalankan Aplikasi

#### Mode Development
```bash
npm run dev
```

#### Mode Production
```bash
npm start
```

### 6. Akses Aplikasi

- **Frontend (Pasien)**: http://localhost:3000
- **Admin Dashboard**: http://localhost:3000/admin
- **API Documentation**: http://localhost:3000/api/health

## 👤 Akun Default

### Admin
- **Username**: admin
- **Password**: admin123

## 🔧 Konfigurasi Tambahan

### Email Notification
Untuk mengaktifkan notifikasi email:

1. **Gmail**: 
   - Aktifkan 2-Factor Authentication
   - Generate App Password
   - Gunakan App Password di `EMAIL_PASS`

2. **SMTP Lain**:
   - Sesuaikan `EMAIL_HOST` dan `EMAIL_PORT`
   - Gunakan kredensial yang sesuai

### PDF Generation
Sistem menggunakan Puppeteer untuk generate PDF. Jika ada masalah:

```bash
# Install dependencies tambahan untuk Puppeteer
npm install puppeteer --save
```

### File Upload
Pastikan direktori upload memiliki permission yang tepat:

```bash
# Linux/Mac
chmod 755 uploads/
chmod 755 uploads/documents/

# Windows - set permission melalui Properties
```

## 🧪 Testing

### Test Database Connection
```bash
node -e "require('./src/models/database').testConnection()"
```

### Test Email Service
```bash
node -e "require('./src/utils/emailService').initializeEmailService()"
```

### Test API Endpoints
```bash
# Health check
curl http://localhost:3000/api/health

# Get examination types
curl http://localhost:3000/api/examinations
```

## 📱 Mobile Responsiveness

Sistem sudah dioptimasi untuk mobile devices. Test di:
- Chrome DevTools (F12 → Toggle Device Toolbar)
- Berbagai ukuran layar (320px - 1920px)
- Portrait dan landscape mode

## 🔒 Keamanan

### Checklist Keamanan:
- [x] Input validation dan sanitization
- [x] SQL injection prevention
- [x] XSS protection
- [x] CSRF protection
- [x] Rate limiting
- [x] JWT authentication
- [x] Password hashing (bcrypt)
- [x] Secure headers (Helmet.js)

### Untuk Production:
1. Ganti `JWT_SECRET` dengan string yang aman
2. Set `NODE_ENV=production`
3. Gunakan HTTPS
4. Setup firewall
5. Regular backup database
6. Monitor logs

## 🚨 Troubleshooting

### Database Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```
**Solusi**: Pastikan MySQL service berjalan di XAMPP

### Port Already in Use
```
Error: listen EADDRINUSE :::3000
```
**Solusi**: 
```bash
# Ganti port di .env
PORT=3001

# Atau kill process yang menggunakan port 3000
netstat -ano | findstr :3000
taskkill /PID <PID> /F
```

### Puppeteer Installation Error
```bash
# Install ulang Puppeteer
npm uninstall puppeteer
npm install puppeteer --save
```

### Email Not Sending
1. Cek konfigurasi SMTP
2. Verifikasi kredensial email
3. Cek firewall/antivirus
4. Test dengan service email lain

## 📞 Support

Jika mengalami masalah:
1. Cek log error di console
2. Periksa file `.env` configuration
3. Pastikan semua dependencies terinstall
4. Restart aplikasi dan database

## 🔄 Update System

```bash
# Backup database terlebih dahulu
mysqldump -u root -p lab_registration > backup.sql

# Pull update terbaru
git pull origin main

# Install dependencies baru
npm install

# Restart aplikasi
npm run dev
```

## 📊 Monitoring

### Log Files
- Application logs: Console output
- Database logs: MySQL error log
- Access logs: Dapat diaktifkan dengan middleware

### Performance Monitoring
- Monitor CPU dan memory usage
- Database query performance
- Response time API endpoints

---

**Selamat! Sistem Pendaftaran Online Laboratorium siap digunakan! 🎉**
