// Lab Registration System - Frontend JavaScript

// Global variables
let currentStep = 1;
let selectedExaminations = [];
let examinationTypes = [];
let patientData = {};

// API Base URL
const API_BASE_URL = '/api';

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    loadExaminationTypes();
    setupEventListeners();
    setupFormValidation();
    setMinDate();
}

// Setup event listeners
function setupEventListeners() {
    // Registration form submission
    document.getElementById('registrationForm').addEventListener('submit', handleRegistrationSubmit);
    
    // Check registration form submission
    document.getElementById('checkRegistrationForm').addEventListener('submit', handleCheckRegistration);
    
    // NIK input validation
    document.getElementById('nik').addEventListener('input', handleNikInput);
    
    // Examination search
    document.getElementById('examination-search').addEventListener('input', filterExaminations);
    
    // Examination category filter
    document.getElementById('examination-category').addEventListener('change', filterExaminations);
    
    // Date change for available slots
    document.getElementById('examination_date').addEventListener('change', loadAvailableSlots);
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Setup form validation
function setupFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// Set minimum date for examination
function setMinDate() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const maxDate = new Date(today);
    maxDate.setDate(maxDate.getDate() + 30);
    
    const examDateInput = document.getElementById('examination_date');
    examDateInput.min = tomorrow.toISOString().split('T')[0];
    examDateInput.max = maxDate.toISOString().split('T')[0];
}

// Load examination types from API
async function loadExaminationTypes() {
    try {
        showLoading();
        const response = await fetch(`${API_BASE_URL}/examinations`);
        const data = await response.json();
        
        if (response.ok) {
            examinationTypes = data.examinations;
            renderExaminationTypes();
            renderServiceCards();
        } else {
            showAlert('error', 'Gagal memuat jenis pemeriksaan');
        }
    } catch (error) {
        console.error('Error loading examination types:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat data');
    } finally {
        hideLoading();
    }
}

// Render examination types in selection step
function renderExaminationTypes() {
    const container = document.getElementById('examination-list');
    container.innerHTML = '';
    
    examinationTypes.forEach(exam => {
        const examElement = createExaminationElement(exam);
        container.appendChild(examElement);
    });
}

// Create examination element
function createExaminationElement(exam) {
    const div = document.createElement('div');
    div.className = 'examination-item';
    div.dataset.examId = exam.id;
    div.onclick = () => toggleExamination(exam);
    
    div.innerHTML = `
        <i class="bi bi-check-circle-fill check-icon"></i>
        <div class="examination-name">${exam.name}</div>
        <div class="examination-description">${exam.description || ''}</div>
        <div class="examination-price">Rp ${formatCurrency(exam.price)}</div>
    `;
    
    return div;
}

// Toggle examination selection
function toggleExamination(exam) {
    const index = selectedExaminations.findIndex(e => e.id === exam.id);
    const element = document.querySelector(`[data-exam-id="${exam.id}"]`);
    
    if (index > -1) {
        // Remove from selection
        selectedExaminations.splice(index, 1);
        element.classList.remove('selected');
    } else {
        // Add to selection
        selectedExaminations.push(exam);
        element.classList.add('selected');
    }
    
    updateSelectedExaminations();
}

// Update selected examinations display
function updateSelectedExaminations() {
    const container = document.getElementById('selected-examinations');
    const listContainer = document.getElementById('selected-list');
    const totalPriceElement = document.getElementById('total-price');
    const nextButton = document.getElementById('next-to-schedule');
    
    if (selectedExaminations.length === 0) {
        container.style.display = 'none';
        nextButton.disabled = true;
        return;
    }
    
    container.style.display = 'block';
    nextButton.disabled = false;
    
    // Render selected examinations
    listContainer.innerHTML = '';
    let totalPrice = 0;
    
    selectedExaminations.forEach(exam => {
        totalPrice += parseFloat(exam.price);
        
        const div = document.createElement('div');
        div.className = 'selected-examination-item';
        div.innerHTML = `
            <div>
                <strong>${exam.name}</strong><br>
                <small class="text-muted">Rp ${formatCurrency(exam.price)}</small>
            </div>
            <i class="bi bi-x-circle remove-examination" onclick="removeExamination(${exam.id})"></i>
        `;
        
        listContainer.appendChild(div);
    });
    
    totalPriceElement.textContent = `Rp ${formatCurrency(totalPrice)}`;
}

// Remove examination from selection
function removeExamination(examId) {
    const exam = examinationTypes.find(e => e.id === examId);
    if (exam) {
        toggleExamination(exam);
    }
}

// Filter examinations based on search and category
function filterExaminations() {
    const searchTerm = document.getElementById('examination-search').value.toLowerCase();
    const category = document.getElementById('examination-category').value;
    
    const examElements = document.querySelectorAll('.examination-item');
    
    examElements.forEach(element => {
        const examId = parseInt(element.dataset.examId);
        const exam = examinationTypes.find(e => e.id === examId);
        
        if (!exam) return;
        
        const matchesSearch = exam.name.toLowerCase().includes(searchTerm) || 
                            (exam.description && exam.description.toLowerCase().includes(searchTerm));
        
        let matchesCategory = true;
        if (category) {
            // Simple category matching based on examination codes
            const categoryMapping = {
                'darah': ['LAB001', 'LAB003', 'LAB004', 'LAB005', 'LAB008', 'LAB009'],
                'urine': ['LAB002'],
                'fungsi-organ': ['LAB006', 'LAB007'],
                'infeksi': ['LAB010']
            };
            
            matchesCategory = categoryMapping[category] && 
                            categoryMapping[category].includes(exam.code);
        }
        
        element.style.display = (matchesSearch && matchesCategory) ? 'block' : 'none';
    });
}

// Render service cards in hero section
function renderServiceCards() {
    const container = document.getElementById('examination-types');
    container.innerHTML = '';
    
    // Group examinations by category
    const categories = [
        { name: 'Pemeriksaan Darah', icon: 'bi-droplet', codes: ['LAB001', 'LAB003', 'LAB004', 'LAB005', 'LAB008', 'LAB009'] },
        { name: 'Pemeriksaan Urine', icon: 'bi-cup', codes: ['LAB002'] },
        { name: 'Fungsi Organ', icon: 'bi-heart-pulse', codes: ['LAB006', 'LAB007'] },
        { name: 'Pemeriksaan Infeksi', icon: 'bi-shield-check', codes: ['LAB010'] }
    ];
    
    categories.forEach(category => {
        const exams = examinationTypes.filter(exam => 
            category.codes.includes(exam.code)
        );
        
        if (exams.length > 0) {
            const div = document.createElement('div');
            div.className = 'col-md-6 col-lg-3 mb-4';
            div.innerHTML = `
                <div class="service-card">
                    <i class="${category.icon} service-icon"></i>
                    <h5>${category.name}</h5>
                    <p>${exams.length} jenis pemeriksaan tersedia</p>
                    <p class="text-muted">Mulai dari Rp ${formatCurrency(Math.min(...exams.map(e => e.price)))}</p>
                </div>
            `;
            container.appendChild(div);
        }
    });
}

// Handle NIK input and check existing patient
async function handleNikInput(event) {
    const nik = event.target.value;
    
    if (nik.length === 16) {
        try {
            const response = await fetch(`${API_BASE_URL}/patients/check-nik/${nik}`);
            const data = await response.json();
            
            if (response.ok && data.exists) {
                // Fill form with existing patient data
                fillPatientForm(data.patient);
                showAlert('info', 'Data pasien ditemukan dan telah diisi otomatis');
            }
        } catch (error) {
            console.error('Error checking NIK:', error);
        }
    }
}

// Fill patient form with existing data
function fillPatientForm(patient) {
    document.getElementById('full_name').value = patient.full_name || '';
    document.getElementById('phone').value = patient.phone || '';
    document.getElementById('email').value = patient.email || '';
}

// Load available time slots for selected date
async function loadAvailableSlots() {
    const date = document.getElementById('examination_date').value;
    if (!date) return;
    
    try {
        const response = await fetch(`${API_BASE_URL}/registrations/available-slots/${date}`);
        const data = await response.json();
        
        if (response.ok) {
            const timeSelect = document.getElementById('examination_time');
            timeSelect.innerHTML = '<option value="">Pilih waktu</option>';
            
            data.available_slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot;
                option.textContent = slot;
                timeSelect.appendChild(option);
            });
            
            if (data.available_slots.length === 0) {
                timeSelect.innerHTML = '<option value="">Tidak ada slot tersedia</option>';
            }
        }
    } catch (error) {
        console.error('Error loading available slots:', error);
        showAlert('error', 'Gagal memuat slot waktu tersedia');
    }
}

// Step navigation functions
function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < 3) {
            currentStep++;
            updateStepDisplay();
            
            if (currentStep === 3) {
                updateRegistrationSummary();
            }
        }
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

// Validate current step
function validateCurrentStep() {
    const currentStepElement = document.querySelector(`.form-step[data-step="${currentStep}"]`);
    const inputs = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
    
    let isValid = true;
    inputs.forEach(input => {
        if (!input.checkValidity()) {
            isValid = false;
            input.classList.add('is-invalid');
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    if (currentStep === 2 && selectedExaminations.length === 0) {
        showAlert('warning', 'Pilih minimal satu jenis pemeriksaan');
        isValid = false;
    }
    
    return isValid;
}

// Update step display
function updateStepDisplay() {
    // Update progress steps
    document.querySelectorAll('.step').forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active', 'completed');
        
        if (stepNumber === currentStep) {
            step.classList.add('active');
        } else if (stepNumber < currentStep) {
            step.classList.add('completed');
        }
    });
    
    // Update form steps
    document.querySelectorAll('.form-step').forEach(step => {
        step.classList.remove('active');
    });
    
    document.querySelector(`.form-step[data-step="${currentStep}"]`).classList.add('active');
}

// Update registration summary
function updateRegistrationSummary() {
    const summaryContainer = document.getElementById('registration-summary');
    const formData = new FormData(document.getElementById('registrationForm'));

    const patientName = formData.get('full_name');
    const examDate = formData.get('examination_date');
    const examTime = formData.get('examination_time');

    const totalPrice = selectedExaminations.reduce((sum, exam) => sum + parseFloat(exam.price), 0);

    summaryContainer.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>Nama Pasien:</strong><br>
                ${patientName}<br><br>
                <strong>Tanggal & Waktu:</strong><br>
                ${formatDate(examDate)} - ${examTime}
            </div>
            <div class="col-md-6">
                <strong>Pemeriksaan:</strong><br>
                ${selectedExaminations.map(exam => exam.name).join(', ')}<br><br>
                <strong>Total Biaya:</strong><br>
                <span class="text-primary fw-bold">Rp ${formatCurrency(totalPrice)}</span>
            </div>
        </div>
    `;
}

// Handle registration form submission
async function handleRegistrationSubmit(event) {
    event.preventDefault();

    if (!validateCurrentStep()) {
        return;
    }

    const formData = new FormData(event.target);
    const registrationData = {
        // Patient data
        nik: formData.get('nik'),
        full_name: formData.get('full_name'),
        birth_date: formData.get('birth_date'),
        gender: formData.get('gender'),
        phone: formData.get('phone'),
        email: formData.get('email') || null,
        address: formData.get('address'),
        bpjs_number: formData.get('bpjs_number') || null,
        emergency_contact_name: formData.get('emergency_contact_name') || null,
        emergency_contact_phone: formData.get('emergency_contact_phone') || null,

        // Registration data
        examination_date: formData.get('examination_date'),
        examination_time: formData.get('examination_time'),
        examination_types: selectedExaminations.map(exam => exam.id),
        notes: formData.get('notes') || null
    };

    try {
        showLoading();

        // First, register or get patient
        let patientId;
        const patientResponse = await fetch(`${API_BASE_URL}/patients/check-nik/${registrationData.nik}`);
        const patientData = await patientResponse.json();

        if (patientData.exists) {
            patientId = patientData.patient.id;
        } else {
            // Register new patient
            const newPatientResponse = await fetch(`${API_BASE_URL}/patients/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    nik: registrationData.nik,
                    full_name: registrationData.full_name,
                    birth_date: registrationData.birth_date,
                    gender: registrationData.gender,
                    phone: registrationData.phone,
                    email: registrationData.email,
                    address: registrationData.address,
                    bpjs_number: registrationData.bpjs_number,
                    emergency_contact_name: registrationData.emergency_contact_name,
                    emergency_contact_phone: registrationData.emergency_contact_phone
                })
            });

            const newPatientData = await newPatientResponse.json();

            if (!newPatientResponse.ok) {
                throw new Error(newPatientData.error || 'Gagal mendaftarkan pasien');
            }

            patientId = newPatientData.patient.id;
        }

        // Create registration
        const registrationResponse = await fetch(`${API_BASE_URL}/registrations`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                patient_id: patientId,
                examination_date: registrationData.examination_date,
                examination_time: registrationData.examination_time,
                examination_types: registrationData.examination_types,
                notes: registrationData.notes
            })
        });

        const registrationResult = await registrationResponse.json();

        if (!registrationResponse.ok) {
            throw new Error(registrationResult.error || 'Gagal membuat registrasi');
        }

        // Show success message
        showRegistrationSuccess(registrationResult.registration);

    } catch (error) {
        console.error('Registration error:', error);
        showAlert('error', error.message || 'Terjadi kesalahan saat mendaftar');
    } finally {
        hideLoading();
    }
}

// Show registration success
function showRegistrationSuccess(registration) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle"></i>
                        Pendaftaran Berhasil!
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">Pendaftaran Anda Berhasil</h4>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Detail Registrasi:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Nomor Registrasi:</strong></td>
                                    <td class="text-primary fw-bold">${registration.registration_number}</td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Pasien:</strong></td>
                                    <td>${registration.patient_name}</td>
                                </tr>
                                <tr>
                                    <td><strong>Tanggal Pemeriksaan:</strong></td>
                                    <td>${formatDate(registration.examination_date)}</td>
                                </tr>
                                <tr>
                                    <td><strong>Waktu:</strong></td>
                                    <td>${registration.examination_time}</td>
                                </tr>
                                <tr>
                                    <td><strong>Pemeriksaan:</strong></td>
                                    <td>${registration.examination_names}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Biaya:</strong></td>
                                    <td class="text-primary fw-bold">Rp ${formatCurrency(registration.total_price)}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle"></i>
                        <strong>Penting:</strong> Simpan nomor registrasi Anda untuk cek status dan keperluan lainnya.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="printRegistration('${registration.registration_number}')">
                        <i class="bi bi-printer"></i> Cetak
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // Reset form
    document.getElementById('registrationForm').reset();
    selectedExaminations = [];
    currentStep = 1;
    updateStepDisplay();
    updateSelectedExaminations();
}

// Handle check registration
async function handleCheckRegistration(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const registrationNumber = formData.get('registration_number');

    if (!registrationNumber) {
        showAlert('warning', 'Masukkan nomor registrasi');
        return;
    }

    try {
        showLoading();

        const response = await fetch(`${API_BASE_URL}/registrations/number/${registrationNumber}`);
        const data = await response.json();

        if (response.ok) {
            displayRegistrationStatus(data.registration);
        } else {
            showAlert('error', data.error || 'Registrasi tidak ditemukan');
        }
    } catch (error) {
        console.error('Check registration error:', error);
        showAlert('error', 'Terjadi kesalahan saat mengecek status');
    } finally {
        hideLoading();
    }
}

// Display registration status
function displayRegistrationStatus(registration) {
    const container = document.getElementById('registration-status');

    const statusClass = `status-${registration.status}`;
    const statusText = {
        'pending': 'Menunggu Konfirmasi',
        'confirmed': 'Dikonfirmasi',
        'completed': 'Selesai',
        'cancelled': 'Dibatalkan'
    };

    container.innerHTML = `
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Status Registrasi</h6>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Nomor Registrasi:</strong></td>
                        <td>${registration.registration_number}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td><span class="status-badge ${statusClass}">${statusText[registration.status]}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Nama Pasien:</strong></td>
                        <td>${registration.patient_name}</td>
                    </tr>
                    <tr>
                        <td><strong>Tanggal Pemeriksaan:</strong></td>
                        <td>${formatDate(registration.examination_date)}</td>
                    </tr>
                    <tr>
                        <td><strong>Waktu:</strong></td>
                        <td>${registration.examination_time}</td>
                    </tr>
                    <tr>
                        <td><strong>Pemeriksaan:</strong></td>
                        <td>${registration.examination_names}</td>
                    </tr>
                    <tr>
                        <td><strong>Total Biaya:</strong></td>
                        <td class="text-primary fw-bold">Rp ${formatCurrency(registration.total_price)}</td>
                    </tr>
                </table>

                ${registration.notes ? `
                    <div class="alert alert-info">
                        <strong>Catatan:</strong> ${registration.notes}
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    container.style.display = 'block';
    container.scrollIntoView({ behavior: 'smooth' });
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('id-ID').format(amount);
}

function formatDate(dateString) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        timeZone: 'Asia/Jakarta'
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of main content
    const main = document.querySelector('main') || document.body;
    main.insertBefore(alertDiv, main.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showLoading() {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
    if (modal) {
        modal.hide();
    }
}

function printRegistration(registrationNumber) {
    window.open(`/print-registration/${registrationNumber}`, '_blank');
}
