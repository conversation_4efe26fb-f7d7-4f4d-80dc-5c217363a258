/* Admin Dashboard CSS */

:root {
    --sidebar-width: 250px;
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.sidebar {
    position: fixed;
    top: 56px; /* Height of navbar */
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - 56px);
    background-color: #fff;
    border-right: 1px solid #dee2e6;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar-menu {
    padding: 1rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
    text-decoration: none;
}

.menu-item.active {
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.menu-item i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
}

.main-content {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    min-height: calc(100vh - 56px);
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0b5ed7);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.stat-card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
    color: #000;
}

.stat-card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
    color: #000;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.stat-card .stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    opacity: 0.3;
}

.stat-card .stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Tables */
.table {
    background: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Popular Examinations */
.popular-exam-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.popular-exam-item:last-child {
    border-bottom: none;
}

.popular-exam-name {
    font-weight: 600;
    color: var(--dark-color);
}

.popular-exam-count {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    color: #0b5ed7;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Charts */
#registrationChart {
    max-height: 400px;
}

/* Statistics Summary */
.stat-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #dee2e6;
}

.stat-summary-item:last-child {
    border-bottom: none;
}

.stat-summary-label {
    font-weight: 500;
    color: var(--secondary-color);
}

.stat-summary-value {
    font-weight: 700;
    color: var(--dark-color);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.table-loading {
    text-align: center;
    padding: 2rem;
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .stat-card .stat-content h3 {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        width: 100%;
    }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: white;
        font-size: 1.25rem;
        padding: 0.5rem;
    }
}

/* Form Styles */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Modal Enhancements */
.modal-header {
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .table {
        font-size: 12px;
    }
    
    .status-badge {
        border: 1px solid #000;
        background: white !important;
        color: black !important;
    }
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 3px solid var(--primary-color) !important;
}
