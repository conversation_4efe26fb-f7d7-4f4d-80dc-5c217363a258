const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { query, transaction, generateRegistrationNumber } = require('../models/database');
const { validateExaminationRegistration, sanitizeInput } = require('../middleware/validation');

// Middleware
router.use(sanitizeInput);

// Konfigurasi multer untuk upload dokumen
const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../../uploads/documents');
        try {
            await fs.mkdir(uploadDir, { recursive: true });
            cb(null, uploadDir);
        } catch (error) {
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, `doc-${uniqueSuffix}${ext}`);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
        files: 5 // Maksimal 5 file
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        
        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Hanya file gambar (JPG, PNG) dan dokumen (PDF, DOC, DOCX) yang diizinkan'));
        }
    }
});

// POST - Buat registrasi pemeriksaan baru
router.post('/', validateExaminationRegistration, async (req, res) => {
    try {
        const {
            patient_id,
            examination_date,
            examination_time,
            examination_types,
            notes
        } = req.body;
        
        // Validasi pasien exists
        const patient = await query(
            'SELECT id, full_name FROM patients WHERE id = ?',
            [patient_id]
        );
        
        if (patient.length === 0) {
            return res.status(404).json({ 
                error: 'Pasien tidak ditemukan' 
            });
        }
        
        // Validasi examination types exists
        const examTypes = await query(
            'SELECT id, name, price FROM examination_types WHERE id IN (?) AND is_active = TRUE',
            [examination_types]
        );
        
        if (examTypes.length !== examination_types.length) {
            return res.status(400).json({ 
                error: 'Beberapa jenis pemeriksaan tidak valid atau tidak aktif' 
            });
        }
        
        // Cek apakah sudah ada registrasi di tanggal dan waktu yang sama
        const existingRegistration = await query(
            `SELECT id FROM registrations 
             WHERE patient_id = ? AND examination_date = ? AND examination_time = ? 
             AND status NOT IN ('cancelled')`,
            [patient_id, examination_date, examination_time]
        );
        
        if (existingRegistration.length > 0) {
            return res.status(409).json({ 
                error: 'Sudah ada registrasi di tanggal dan waktu yang sama' 
            });
        }
        
        // Hitung total harga
        const totalPrice = examTypes.reduce((sum, exam) => sum + parseFloat(exam.price), 0);
        
        // Generate nomor registrasi
        const registrationNumber = generateRegistrationNumber();
        
        // Buat transaksi untuk insert registrasi dan detail pemeriksaan
        const queries = [
            {
                sql: `INSERT INTO registrations 
                      (registration_number, patient_id, examination_date, examination_time, 
                       status, notes, total_price, payment_status) 
                      VALUES (?, ?, ?, ?, 'pending', ?, ?, 'unpaid')`,
                params: [registrationNumber, patient_id, examination_date, examination_time, 
                        notes || null, totalPrice]
            }
        ];
        
        const results = await transaction(queries);
        const registrationId = results[0].insertId;
        
        // Insert detail pemeriksaan
        const examQueries = examination_types.map(examTypeId => {
            const examType = examTypes.find(et => et.id === examTypeId);
            return {
                sql: `INSERT INTO registration_examinations 
                      (registration_id, examination_type_id, price) 
                      VALUES (?, ?, ?)`,
                params: [registrationId, examTypeId, examType.price]
            };
        });
        
        await transaction(examQueries);
        
        // Ambil data registrasi lengkap
        const newRegistration = await query(
            `SELECT r.*, p.full_name as patient_name, p.nik, p.phone,
                    GROUP_CONCAT(et.name SEPARATOR ', ') as examination_names
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             WHERE r.id = ?
             GROUP BY r.id`,
            [registrationId]
        );
        
        res.status(201).json({
            message: 'Registrasi berhasil dibuat',
            registration: newRegistration[0]
        });
        
    } catch (error) {
        console.error('Create registration error:', error);
        res.status(500).json({ 
            error: 'Gagal membuat registrasi' 
        });
    }
});

// GET - Ambil registrasi berdasarkan nomor registrasi
router.get('/number/:registrationNumber', async (req, res) => {
    try {
        const { registrationNumber } = req.params;
        
        const registration = await query(
            `SELECT r.*, p.full_name as patient_name, p.nik, p.phone, p.email, p.address,
                    GROUP_CONCAT(CONCAT(et.name, ' (', FORMAT(re.price, 0), ')') SEPARATOR ', ') as examination_details,
                    GROUP_CONCAT(et.name SEPARATOR ', ') as examination_names
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             WHERE r.registration_number = ?
             GROUP BY r.id`,
            [registrationNumber]
        );
        
        if (registration.length === 0) {
            return res.status(404).json({ 
                error: 'Registrasi tidak ditemukan' 
            });
        }
        
        // Ambil dokumen yang diupload
        const documents = await query(
            'SELECT id, document_type, original_name, file_name FROM documents WHERE registration_id = ?',
            [registration[0].id]
        );
        
        res.json({
            registration: {
                ...registration[0],
                documents
            }
        });
        
    } catch (error) {
        console.error('Get registration error:', error);
        res.status(500).json({
            error: 'Gagal mengambil data registrasi'
        });
    }
});

// POST - Upload dokumen untuk registrasi
router.post('/:id/documents', upload.array('documents', 5), async (req, res) => {
    try {
        const { id } = req.params;
        const files = req.files;

        if (!files || files.length === 0) {
            return res.status(400).json({
                error: 'Tidak ada file yang diupload'
            });
        }

        // Validasi registrasi exists
        const registration = await query(
            'SELECT id FROM registrations WHERE id = ?',
            [id]
        );

        if (registration.length === 0) {
            return res.status(404).json({
                error: 'Registrasi tidak ditemukan'
            });
        }

        // Insert data dokumen ke database
        const documentQueries = files.map(file => ({
            sql: `INSERT INTO documents
                  (registration_id, document_type, original_name, file_name, file_path, file_size, mime_type)
                  VALUES (?, ?, ?, ?, ?, ?, ?)`,
            params: [
                id,
                req.body.document_type || 'other',
                file.originalname,
                file.filename,
                file.path,
                file.size,
                file.mimetype
            ]
        }));

        await transaction(documentQueries);

        res.json({
            message: 'Dokumen berhasil diupload',
            files: files.map(file => ({
                original_name: file.originalname,
                file_name: file.filename,
                size: file.size
            }))
        });

    } catch (error) {
        console.error('Upload documents error:', error);

        // Hapus file yang sudah diupload jika terjadi error
        if (req.files) {
            req.files.forEach(async (file) => {
                try {
                    await fs.unlink(file.path);
                } catch (unlinkError) {
                    console.error('Failed to delete uploaded file:', unlinkError);
                }
            });
        }

        res.status(500).json({
            error: 'Gagal mengupload dokumen'
        });
    }
});

// GET - Ambil daftar slot waktu yang tersedia
router.get('/available-slots/:date', async (req, res) => {
    try {
        const { date } = req.params;

        // Validasi format tanggal
        if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
            return res.status(400).json({
                error: 'Format tanggal tidak valid (YYYY-MM-DD)'
            });
        }

        // Ambil registrasi yang sudah ada di tanggal tersebut
        const existingRegistrations = await query(
            `SELECT examination_time FROM registrations
             WHERE examination_date = ? AND status NOT IN ('cancelled')`,
            [date]
        );

        const bookedSlots = existingRegistrations.map(reg => reg.examination_time);

        // Generate slot waktu dari 07:00 - 16:00 (interval 30 menit)
        const availableSlots = [];
        for (let hour = 7; hour <= 16; hour++) {
            for (let minute = 0; minute < 60; minute += 30) {
                if (hour === 16 && minute > 0) break; // Batas sampai 16:00

                const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

                if (!bookedSlots.includes(timeSlot)) {
                    availableSlots.push(timeSlot);
                }
            }
        }

        res.json({
            date,
            available_slots: availableSlots,
            booked_slots: bookedSlots
        });

    } catch (error) {
        console.error('Get available slots error:', error);
        res.status(500).json({
            error: 'Gagal mengambil slot waktu'
        });
    }
});

module.exports = router;
