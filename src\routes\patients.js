const express = require('express');
const router = express.Router();
const { query, transaction } = require('../models/database');
const { validatePatientRegistration, sanitizeInput } = require('../middleware/validation');

// Middleware
router.use(sanitizeInput);

// GET - Cek apakah NIK sudah terdaftar
router.get('/check-nik/:nik', async (req, res) => {
    try {
        const { nik } = req.params;
        
        if (!/^\d{16}$/.test(nik)) {
            return res.status(400).json({ 
                error: 'NIK harus 16 digit angka' 
            });
        }
        
        const patient = await query(
            'SELECT id, full_name, phone, email FROM patients WHERE nik = ?',
            [nik]
        );
        
        res.json({
            exists: patient.length > 0,
            patient: patient.length > 0 ? patient[0] : null
        });
    } catch (error) {
        console.error('Check NIK error:', error);
        res.status(500).json({ 
            error: 'Gagal memeriksa NIK' 
        });
    }
});

// POST - Registrasi pasien baru
router.post('/register', validatePatientRegistration, async (req, res) => {
    try {
        const {
            nik, full_name, birth_date, gender, phone, email,
            address, bpjs_number, emergency_contact_name, emergency_contact_phone
        } = req.body;
        
        // Cek apakah NIK sudah terdaftar
        const existingPatient = await query(
            'SELECT id FROM patients WHERE nik = ?',
            [nik]
        );
        
        if (existingPatient.length > 0) {
            return res.status(409).json({ 
                error: 'NIK sudah terdaftar dalam sistem' 
            });
        }
        
        // Insert pasien baru
        const result = await query(
            `INSERT INTO patients 
             (nik, full_name, birth_date, gender, phone, email, address, 
              bpjs_number, emergency_contact_name, emergency_contact_phone) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [nik, full_name, birth_date, gender, phone, email || null, address,
             bpjs_number || null, emergency_contact_name || null, emergency_contact_phone || null]
        );
        
        // Ambil data pasien yang baru dibuat
        const newPatient = await query(
            'SELECT * FROM patients WHERE id = ?',
            [result.insertId]
        );
        
        res.status(201).json({
            message: 'Pasien berhasil didaftarkan',
            patient: newPatient[0]
        });
        
    } catch (error) {
        console.error('Patient registration error:', error);
        
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(409).json({ 
                error: 'NIK atau email sudah terdaftar' 
            });
        }
        
        res.status(500).json({ 
            error: 'Gagal mendaftarkan pasien' 
        });
    }
});

// GET - Ambil data pasien berdasarkan ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!/^\d+$/.test(id)) {
            return res.status(400).json({ 
                error: 'ID pasien tidak valid' 
            });
        }
        
        const patient = await query(
            'SELECT * FROM patients WHERE id = ?',
            [id]
        );
        
        if (patient.length === 0) {
            return res.status(404).json({ 
                error: 'Pasien tidak ditemukan' 
            });
        }
        
        res.json({ patient: patient[0] });
        
    } catch (error) {
        console.error('Get patient error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil data pasien' 
        });
    }
});

// PUT - Update data pasien
router.put('/:id', validatePatientRegistration, async (req, res) => {
    try {
        const { id } = req.params;
        const {
            nik, full_name, birth_date, gender, phone, email,
            address, bpjs_number, emergency_contact_name, emergency_contact_phone
        } = req.body;
        
        if (!/^\d+$/.test(id)) {
            return res.status(400).json({ 
                error: 'ID pasien tidak valid' 
            });
        }
        
        // Cek apakah pasien ada
        const existingPatient = await query(
            'SELECT id FROM patients WHERE id = ?',
            [id]
        );
        
        if (existingPatient.length === 0) {
            return res.status(404).json({ 
                error: 'Pasien tidak ditemukan' 
            });
        }
        
        // Cek apakah NIK sudah digunakan pasien lain
        const nikCheck = await query(
            'SELECT id FROM patients WHERE nik = ? AND id != ?',
            [nik, id]
        );
        
        if (nikCheck.length > 0) {
            return res.status(409).json({ 
                error: 'NIK sudah digunakan pasien lain' 
            });
        }
        
        // Update data pasien
        await query(
            `UPDATE patients SET 
             nik = ?, full_name = ?, birth_date = ?, gender = ?, phone = ?, 
             email = ?, address = ?, bpjs_number = ?, emergency_contact_name = ?, 
             emergency_contact_phone = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [nik, full_name, birth_date, gender, phone, email || null, address,
             bpjs_number || null, emergency_contact_name || null, 
             emergency_contact_phone || null, id]
        );
        
        // Ambil data pasien yang sudah diupdate
        const updatedPatient = await query(
            'SELECT * FROM patients WHERE id = ?',
            [id]
        );
        
        res.json({
            message: 'Data pasien berhasil diperbarui',
            patient: updatedPatient[0]
        });
        
    } catch (error) {
        console.error('Update patient error:', error);
        
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(409).json({ 
                error: 'NIK atau email sudah digunakan' 
            });
        }
        
        res.status(500).json({ 
            error: 'Gagal memperbarui data pasien' 
        });
    }
});

// GET - Cari pasien berdasarkan NIK atau nama
router.get('/search/:query', async (req, res) => {
    try {
        const { query: searchQuery } = req.params;
        
        if (searchQuery.length < 3) {
            return res.status(400).json({ 
                error: 'Query pencarian minimal 3 karakter' 
            });
        }
        
        let sql, params;
        
        // Jika query adalah angka, cari berdasarkan NIK
        if (/^\d+$/.test(searchQuery)) {
            sql = 'SELECT * FROM patients WHERE nik LIKE ? ORDER BY created_at DESC LIMIT 10';
            params = [`%${searchQuery}%`];
        } else {
            // Jika bukan angka, cari berdasarkan nama
            sql = 'SELECT * FROM patients WHERE full_name LIKE ? ORDER BY created_at DESC LIMIT 10';
            params = [`%${searchQuery}%`];
        }
        
        const patients = await query(sql, params);
        
        res.json({ patients });
        
    } catch (error) {
        console.error('Search patients error:', error);
        res.status(500).json({ 
            error: 'Gagal mencari pasien' 
        });
    }
});

module.exports = router;
