const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// Rate limiting untuk API endpoints
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
        error: 'Terlalu banyak permintaan dari IP ini, coba lagi nanti.',
        retryAfter: '15 menit'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        // Skip rate limiting untuk admin yang sudah login
        return req.admin && req.admin.role === 'super_admin';
    }
});

// Rate limiting khusus untuk login
const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 login attempts per windowMs
    message: {
        error: 'Terlalu banyak percobaan login, coba lagi dalam 15 menit.',
        retryAfter: '15 menit'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true
});

// Rate limiting untuk registrasi pasien
const registrationLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // limit each IP to 10 registrations per hour
    message: {
        error: 'Terlalu banyak registrasi dari IP ini, coba lagi dalam 1 jam.',
        retryAfter: '1 jam'
    },
    standardHeaders: true,
    legacyHeaders: false
});

// Helmet configuration untuk security headers
const helmetConfig = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: [
                "'self'", 
                "'unsafe-inline'", 
                "https://cdn.jsdelivr.net", 
                "https://cdnjs.cloudflare.com"
            ],
            scriptSrc: [
                "'self'", 
                "'unsafe-inline'", 
                "https://cdn.jsdelivr.net", 
                "https://cdnjs.cloudflare.com"
            ],
            imgSrc: ["'self'", "data:", "https:"],
            fontSrc: [
                "'self'", 
                "https://cdn.jsdelivr.net", 
                "https://cdnjs.cloudflare.com"
            ],
            connectSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"]
        }
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
});

// Middleware untuk validasi input dan mencegah injection
const sanitizeInput = (req, res, next) => {
    const sanitize = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string') {
                // Remove potentially dangerous characters
                obj[key] = obj[key]
                    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                    .replace(/<[^>]*>/g, '')
                    .replace(/javascript:/gi, '')
                    .replace(/on\w+\s*=/gi, '')
                    .trim();
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitize(obj[key]);
            }
        }
    };
    
    if (req.body) sanitize(req.body);
    if (req.query) sanitize(req.query);
    if (req.params) sanitize(req.params);
    
    next();
};

// Middleware untuk validasi NIK Indonesia
const validateNIK = (req, res, next) => {
    const nik = req.body.nik || req.params.nik;
    
    if (nik) {
        // NIK harus 16 digit angka
        if (!/^\d{16}$/.test(nik)) {
            return res.status(400).json({
                error: 'NIK harus terdiri dari 16 digit angka'
            });
        }
        
        // Validasi format NIK Indonesia
        const provinceCode = nik.substring(0, 2);
        const cityCode = nik.substring(2, 4);
        const districtCode = nik.substring(4, 6);
        const birthDate = nik.substring(6, 12);
        
        // Validasi kode provinsi (01-94)
        const provinceCodeNum = parseInt(provinceCode);
        if (provinceCodeNum < 1 || provinceCodeNum > 94) {
            return res.status(400).json({
                error: 'Kode provinsi dalam NIK tidak valid'
            });
        }
        
        // Validasi tanggal lahir dalam NIK
        const day = parseInt(birthDate.substring(0, 2));
        const month = parseInt(birthDate.substring(2, 4));
        const year = parseInt('20' + birthDate.substring(4, 6));
        
        // Untuk perempuan, tanggal ditambah 40
        const actualDay = day > 40 ? day - 40 : day;
        
        if (actualDay < 1 || actualDay > 31 || month < 1 || month > 12) {
            return res.status(400).json({
                error: 'Tanggal lahir dalam NIK tidak valid'
            });
        }
    }
    
    next();
};

// Middleware untuk validasi nomor telepon Indonesia
const validatePhoneNumber = (req, res, next) => {
    const phone = req.body.phone || req.body.emergency_contact_phone;
    
    if (phone) {
        // Format nomor telepon Indonesia
        const phoneRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
        
        if (!phoneRegex.test(phone.replace(/\s|-/g, ''))) {
            return res.status(400).json({
                error: 'Format nomor telepon tidak valid. Gunakan format Indonesia (08xxxxxxxxx)'
            });
        }
    }
    
    next();
};

// Middleware untuk validasi email
const validateEmail = (req, res, next) => {
    const email = req.body.email;
    
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                error: 'Format email tidak valid'
            });
        }
        
        // Cek domain email yang umum digunakan
        const allowedDomains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'yahoo.co.id', 'gmail.co.id', 'live.com', 'icloud.com'
        ];
        
        const domain = email.split('@')[1].toLowerCase();
        // Tidak wajib menggunakan domain yang diizinkan, hanya warning
        if (!allowedDomains.includes(domain)) {
            console.warn(`Unusual email domain: ${domain}`);
        }
    }
    
    next();
};

// Middleware untuk logging aktivitas mencurigakan
const logSuspiciousActivity = (req, res, next) => {
    const suspiciousPatterns = [
        /union\s+select/i,
        /drop\s+table/i,
        /insert\s+into/i,
        /delete\s+from/i,
        /<script/i,
        /javascript:/i,
        /eval\(/i,
        /alert\(/i
    ];
    
    const checkSuspicious = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string') {
                for (let pattern of suspiciousPatterns) {
                    if (pattern.test(obj[key])) {
                        console.warn(`Suspicious activity detected from IP ${req.ip}:`, {
                            field: key,
                            value: obj[key],
                            userAgent: req.get('User-Agent'),
                            timestamp: new Date().toISOString()
                        });
                        break;
                    }
                }
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                checkSuspicious(obj[key]);
            }
        }
    };
    
    if (req.body) checkSuspicious(req.body);
    if (req.query) checkSuspicious(req.query);
    
    next();
};

// Middleware untuk CORS yang aman
const corsConfig = {
    origin: function (origin, callback) {
        // Daftar domain yang diizinkan
        const allowedOrigins = [
            'http://localhost:3000',
            'http://127.0.0.1:3000',
            'http://localhost:8080',
            // Tambahkan domain production di sini
        ];
        
        // Izinkan request tanpa origin (mobile apps, postman, etc.)
        if (!origin) return callback(null, true);
        
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// Middleware untuk mencegah brute force attack
const bruteForceProtection = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    const attempts = new Map();
    
    return (req, res, next) => {
        const key = req.ip + ':' + (req.body.username || req.body.nik || 'anonymous');
        const now = Date.now();
        
        if (!attempts.has(key)) {
            attempts.set(key, { count: 1, resetTime: now + windowMs });
            return next();
        }
        
        const userAttempts = attempts.get(key);
        
        if (now > userAttempts.resetTime) {
            userAttempts.count = 1;
            userAttempts.resetTime = now + windowMs;
            return next();
        }
        
        if (userAttempts.count >= maxAttempts) {
            return res.status(429).json({
                error: 'Terlalu banyak percobaan gagal. Coba lagi nanti.',
                retryAfter: Math.ceil((userAttempts.resetTime - now) / 1000)
            });
        }
        
        userAttempts.count++;
        next();
    };
};

module.exports = {
    apiLimiter,
    loginLimiter,
    registrationLimiter,
    helmetConfig,
    sanitizeInput,
    validateNIK,
    validatePhoneNumber,
    validateEmail,
    logSuspiciousActivity,
    corsConfig,
    bruteForceProtection
};
