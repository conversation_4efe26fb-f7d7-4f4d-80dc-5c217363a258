const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { query, transaction } = require('../models/database');
const { verifyToken, requireRole, logActivity } = require('../middleware/auth');
const { validateAdminLogin, validateRegistrationStatus, sanitizeInput } = require('../middleware/validation');

// Middleware
router.use(sanitizeInput);

// POST - Login admin
router.post('/login', validateAdminLogin, async (req, res) => {
    try {
        const { username, password } = req.body;
        
        // Cari admin berdasarkan username
        const admin = await query(
            'SELECT id, username, email, password, full_name, role FROM admins WHERE username = ?',
            [username]
        );
        
        if (admin.length === 0) {
            return res.status(401).json({ 
                error: 'Username atau password salah' 
            });
        }
        
        // Verifikasi password
        const isValidPassword = await bcrypt.compare(password, admin[0].password);
        
        if (!isValidPassword) {
            return res.status(401).json({ 
                error: 'Username atau password salah' 
            });
        }
        
        // Generate JWT token
        const token = jwt.sign(
            { 
                id: admin[0].id, 
                username: admin[0].username, 
                role: admin[0].role 
            },
            process.env.JWT_SECRET,
            { expiresIn: '8h' }
        );
        
        // Log aktivitas login
        await query(
            `INSERT INTO admin_logs (admin_id, action, ip_address, user_agent, created_at) 
             VALUES (?, 'login', ?, ?, NOW())`,
            [admin[0].id, req.ip, req.get('User-Agent')]
        ).catch(error => {
            console.error('Failed to log login activity:', error);
        });
        
        res.json({
            message: 'Login berhasil',
            token,
            admin: {
                id: admin[0].id,
                username: admin[0].username,
                email: admin[0].email,
                full_name: admin[0].full_name,
                role: admin[0].role
            }
        });
        
    } catch (error) {
        console.error('Admin login error:', error);
        res.status(500).json({ 
            error: 'Gagal melakukan login' 
        });
    }
});

// GET - Dashboard statistik
router.get('/dashboard', verifyToken, async (req, res) => {
    try {
        // Statistik hari ini
        const todayStats = await query(
            `SELECT 
                COUNT(*) as total_registrations,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_registrations,
                COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_registrations,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_registrations,
                SUM(CASE WHEN payment_status = 'paid' THEN total_price ELSE 0 END) as total_revenue
             FROM registrations 
             WHERE DATE(created_at) = CURDATE()`
        );
        
        // Statistik bulan ini
        const monthlyStats = await query(
            `SELECT 
                COUNT(*) as total_registrations,
                COUNT(DISTINCT patient_id) as unique_patients,
                SUM(CASE WHEN payment_status = 'paid' THEN total_price ELSE 0 END) as total_revenue,
                AVG(total_price) as avg_registration_value
             FROM registrations 
             WHERE MONTH(created_at) = MONTH(CURDATE()) 
             AND YEAR(created_at) = YEAR(CURDATE())`
        );
        
        // Registrasi terbaru
        const recentRegistrations = await query(
            `SELECT r.id, r.registration_number, r.examination_date, r.status, 
                    p.full_name as patient_name, p.phone,
                    GROUP_CONCAT(et.name SEPARATOR ', ') as examination_names
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             GROUP BY r.id
             ORDER BY r.created_at DESC
             LIMIT 10`
        );
        
        // Pemeriksaan populer bulan ini
        const popularExaminations = await query(
            `SELECT et.name, COUNT(re.id) as total_orders, SUM(re.price) as total_revenue
             FROM examination_types et
             JOIN registration_examinations re ON et.id = re.examination_type_id
             JOIN registrations r ON re.registration_id = r.id
             WHERE MONTH(r.created_at) = MONTH(CURDATE()) 
             AND YEAR(r.created_at) = YEAR(CURDATE())
             GROUP BY et.id, et.name
             ORDER BY total_orders DESC
             LIMIT 5`
        );
        
        res.json({
            today: todayStats[0],
            monthly: monthlyStats[0],
            recent_registrations: recentRegistrations,
            popular_examinations: popularExaminations
        });
        
    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil data dashboard' 
        });
    }
});

// GET - Daftar semua registrasi dengan filter
router.get('/registrations', verifyToken, async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 20, 
            status, 
            date_from, 
            date_to, 
            search 
        } = req.query;
        
        const offset = (page - 1) * limit;
        
        // Build query dengan filter
        let whereConditions = [];
        let queryParams = [];
        
        if (status) {
            whereConditions.push('r.status = ?');
            queryParams.push(status);
        }
        
        if (date_from) {
            whereConditions.push('r.examination_date >= ?');
            queryParams.push(date_from);
        }
        
        if (date_to) {
            whereConditions.push('r.examination_date <= ?');
            queryParams.push(date_to);
        }
        
        if (search) {
            whereConditions.push('(p.full_name LIKE ? OR p.nik LIKE ? OR r.registration_number LIKE ?)');
            queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
        
        // Query utama
        const registrations = await query(
            `SELECT r.*, p.full_name as patient_name, p.nik, p.phone,
                    GROUP_CONCAT(et.name SEPARATOR ', ') as examination_names
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             ${whereClause}
             GROUP BY r.id
             ORDER BY r.created_at DESC
             LIMIT ? OFFSET ?`,
            [...queryParams, parseInt(limit), offset]
        );
        
        // Query untuk total count
        const totalCount = await query(
            `SELECT COUNT(DISTINCT r.id) as total
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             ${whereClause}`,
            queryParams
        );
        
        res.json({
            registrations,
            pagination: {
                current_page: parseInt(page),
                per_page: parseInt(limit),
                total: totalCount[0].total,
                total_pages: Math.ceil(totalCount[0].total / limit)
            }
        });
        
    } catch (error) {
        console.error('Get registrations error:', error);
        res.status(500).json({ 
            error: 'Gagal mengambil data registrasi' 
        });
    }
});

// PUT - Update status registrasi
router.put('/registrations/:id/status', 
    verifyToken, 
    validateRegistrationStatus, 
    logActivity('update_registration_status'),
    async (req, res) => {
        try {
            const { id } = req.params;
            const { status, notes } = req.body;
            
            // Validasi registrasi exists
            const registration = await query(
                'SELECT id, status as current_status FROM registrations WHERE id = ?',
                [id]
            );
            
            if (registration.length === 0) {
                return res.status(404).json({ 
                    error: 'Registrasi tidak ditemukan' 
                });
            }
            
            // Update status
            await query(
                `UPDATE registrations 
                 SET status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?`,
                [status, notes || null, id]
            );
            
            // Ambil data registrasi yang sudah diupdate
            const updatedRegistration = await query(
                `SELECT r.*, p.full_name as patient_name, p.phone, p.email
                 FROM registrations r
                 JOIN patients p ON r.patient_id = p.id
                 WHERE r.id = ?`,
                [id]
            );
            
            res.json({
                message: 'Status registrasi berhasil diperbarui',
                registration: updatedRegistration[0]
            });
            
        } catch (error) {
            console.error('Update registration status error:', error);
            res.status(500).json({
                error: 'Gagal memperbarui status registrasi'
            });
        }
    }
);

// GET - Detail registrasi untuk admin
router.get('/registrations/:id', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;

        const registration = await query(
            `SELECT r.*, p.*,
                    GROUP_CONCAT(CONCAT(et.name, ' - Rp ', FORMAT(re.price, 0)) SEPARATOR '\n') as examination_details
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             WHERE r.id = ?
             GROUP BY r.id`,
            [id]
        );

        if (registration.length === 0) {
            return res.status(404).json({
                error: 'Registrasi tidak ditemukan'
            });
        }

        // Ambil dokumen yang diupload
        const documents = await query(
            'SELECT * FROM documents WHERE registration_id = ?',
            [id]
        );

        res.json({
            registration: {
                ...registration[0],
                documents
            }
        });

    } catch (error) {
        console.error('Get registration detail error:', error);
        res.status(500).json({
            error: 'Gagal mengambil detail registrasi'
        });
    }
});

// GET - Laporan statistik
router.get('/reports/statistics', verifyToken, requireRole(['admin', 'super_admin']), async (req, res) => {
    try {
        const { period = 'monthly', year = new Date().getFullYear() } = req.query;

        let dateCondition = '';
        let groupBy = '';

        if (period === 'daily') {
            dateCondition = `WHERE MONTH(r.created_at) = MONTH(CURDATE()) AND YEAR(r.created_at) = YEAR(CURDATE())`;
            groupBy = 'DATE(r.created_at)';
        } else if (period === 'monthly') {
            dateCondition = `WHERE YEAR(r.created_at) = ?`;
            groupBy = 'MONTH(r.created_at)';
        } else if (period === 'yearly') {
            dateCondition = '';
            groupBy = 'YEAR(r.created_at)';
        }

        const params = period === 'monthly' ? [year] : [];

        const statistics = await query(
            `SELECT
                ${groupBy} as period,
                COUNT(*) as total_registrations,
                COUNT(CASE WHEN r.status = 'completed' THEN 1 END) as completed_registrations,
                SUM(CASE WHEN r.payment_status = 'paid' THEN r.total_price ELSE 0 END) as total_revenue,
                AVG(r.total_price) as avg_registration_value,
                COUNT(DISTINCT r.patient_id) as unique_patients
             FROM registrations r
             ${dateCondition}
             GROUP BY ${groupBy}
             ORDER BY period ASC`,
            params
        );

        res.json({
            period,
            year: period === 'monthly' ? year : null,
            statistics
        });

    } catch (error) {
        console.error('Get statistics report error:', error);
        res.status(500).json({
            error: 'Gagal mengambil laporan statistik'
        });
    }
});

module.exports = router;
