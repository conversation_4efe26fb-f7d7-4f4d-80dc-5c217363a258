const express = require('express');
const router = express.Router();
const puppeteer = require('puppeteer');
const { query } = require('../models/database');
const { generateRegistrationHTML, generateRegistrationReportHTML } = require('../utils/pdfGenerator');
const { verifyToken } = require('../middleware/auth');

// Generate PDF untuk registrasi individual
router.get('/registration/:registrationNumber', async (req, res) => {
    try {
        const { registrationNumber } = req.params;
        
        // Ambil data registrasi lengkap
        const registration = await query(
            `SELECT r.*, p.*, 
                    GROUP_CONCAT(CONCAT(et.name, ' - Rp ', FORMAT(re.price, 0)) SEPARATOR '\n') as examination_details
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             WHERE r.registration_number = ?
             GROUP BY r.id`,
            [registrationNumber]
        );
        
        if (registration.length === 0) {
            return res.status(404).json({ 
                error: 'Registrasi tidak ditemukan' 
            });
        }
        
        // Generate HTML
        const html = generateRegistrationHTML(registration[0]);
        
        // Generate PDF menggunakan Puppeteer
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        await page.setContent(html, { waitUntil: 'networkidle0' });
        
        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20px',
                right: '20px',
                bottom: '20px',
                left: '20px'
            }
        });
        
        await browser.close();
        
        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="registrasi-${registrationNumber}.pdf"`);
        res.setHeader('Content-Length', pdf.length);
        
        res.send(pdf);
        
    } catch (error) {
        console.error('PDF generation error:', error);
        res.status(500).json({ 
            error: 'Gagal generate PDF' 
        });
    }
});

// Generate PDF laporan registrasi (hanya untuk admin)
router.get('/report/registrations', verifyToken, async (req, res) => {
    try {
        const { 
            status, 
            date_from, 
            date_to, 
            search 
        } = req.query;
        
        // Build query dengan filter
        let whereConditions = [];
        let queryParams = [];
        
        if (status) {
            whereConditions.push('r.status = ?');
            queryParams.push(status);
        }
        
        if (date_from) {
            whereConditions.push('r.examination_date >= ?');
            queryParams.push(date_from);
        }
        
        if (date_to) {
            whereConditions.push('r.examination_date <= ?');
            queryParams.push(date_to);
        }
        
        if (search) {
            whereConditions.push('(p.full_name LIKE ? OR p.nik LIKE ? OR r.registration_number LIKE ?)');
            queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
        
        // Query data registrasi
        const registrations = await query(
            `SELECT r.*, p.full_name as patient_name, p.nik, p.phone,
                    GROUP_CONCAT(et.name SEPARATOR ', ') as examination_names
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             ${whereClause}
             GROUP BY r.id
             ORDER BY r.created_at DESC
             LIMIT 1000`,
            queryParams
        );
        
        // Generate HTML
        const filters = { status, date_from, date_to, search };
        const html = generateRegistrationReportHTML(registrations, filters);
        
        // Generate PDF
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        await page.setContent(html, { waitUntil: 'networkidle0' });
        
        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20px',
                right: '20px',
                bottom: '20px',
                left: '20px'
            },
            landscape: true // Landscape untuk tabel yang lebar
        });
        
        await browser.close();
        
        // Generate filename dengan timestamp
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `laporan-registrasi-${timestamp}.pdf`;
        
        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Length', pdf.length);
        
        res.send(pdf);
        
    } catch (error) {
        console.error('Report PDF generation error:', error);
        res.status(500).json({ 
            error: 'Gagal generate laporan PDF' 
        });
    }
});

// Generate PDF untuk hasil pemeriksaan (placeholder)
router.get('/result/:registrationId', verifyToken, async (req, res) => {
    try {
        const { registrationId } = req.params;
        
        // Ambil data registrasi dan hasil pemeriksaan
        const registration = await query(
            `SELECT r.*, p.*, 
                    GROUP_CONCAT(CONCAT(et.name, ' - ', et.description) SEPARATOR '\n') as examination_details
             FROM registrations r
             JOIN patients p ON r.patient_id = p.id
             JOIN registration_examinations re ON r.id = re.registration_id
             JOIN examination_types et ON re.examination_type_id = et.id
             WHERE r.id = ? AND r.status = 'completed'
             GROUP BY r.id`,
            [registrationId]
        );
        
        if (registration.length === 0) {
            return res.status(404).json({ 
                error: 'Registrasi tidak ditemukan atau belum selesai' 
            });
        }
        
        // Template HTML untuk hasil pemeriksaan
        const html = `
        <!DOCTYPE html>
        <html lang="id">
        <head>
            <meta charset="UTF-8">
            <title>Hasil Pemeriksaan - ${registration[0].registration_number}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; border-bottom: 2px solid #0d6efd; padding-bottom: 20px; }
                .patient-info { margin: 20px 0; }
                .results-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .results-table th, .results-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .results-table th { background-color: #f2f2f2; }
                .footer { margin-top: 50px; text-align: center; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>LABORATORIUM KESEHATAN</h1>
                <h2>HASIL PEMERIKSAAN LABORATORIUM</h2>
            </div>
            
            <div class="patient-info">
                <h3>Data Pasien</h3>
                <p><strong>Nama:</strong> ${registration[0].full_name}</p>
                <p><strong>NIK:</strong> ${registration[0].nik}</p>
                <p><strong>Tanggal Pemeriksaan:</strong> ${registration[0].examination_date}</p>
                <p><strong>No. Registrasi:</strong> ${registration[0].registration_number}</p>
            </div>
            
            <h3>Hasil Pemeriksaan</h3>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>Jenis Pemeriksaan</th>
                        <th>Hasil</th>
                        <th>Nilai Normal</th>
                        <th>Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="4" style="text-align: center; color: #666;">
                            Hasil pemeriksaan akan diisi oleh petugas laboratorium
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div class="footer">
                <p>Dokumen ini dicetak pada: ${new Date().toLocaleDateString('id-ID')}</p>
                <p>LABORATORIUM KESEHATAN - Jl. Kesehatan No. 123, Jakarta</p>
            </div>
        </body>
        </html>
        `;
        
        // Generate PDF
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        await page.setContent(html, { waitUntil: 'networkidle0' });
        
        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20px',
                right: '20px',
                bottom: '20px',
                left: '20px'
            }
        });
        
        await browser.close();
        
        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="hasil-${registration[0].registration_number}.pdf"`);
        res.setHeader('Content-Length', pdf.length);
        
        res.send(pdf);
        
    } catch (error) {
        console.error('Result PDF generation error:', error);
        res.status(500).json({ 
            error: 'Gagal generate PDF hasil pemeriksaan' 
        });
    }
});

// Health check untuk PDF service
router.get('/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        service: 'PDF Generator',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
