const fs = require('fs');
const path = require('path');
const { formatDateIndonesia, formatCurrency } = require('../models/database');

// Generate HTML template untuk PDF
function generateRegistrationHTML(registration) {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoExists = fs.existsSync(logoPath);
    
    return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Bukti Registrasi - ${registration.registration_number}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #333;
                line-height: 1.6;
            }
            .header {
                text-align: center;
                border-bottom: 2px solid #0d6efd;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .logo {
                max-width: 100px;
                margin-bottom: 10px;
            }
            .hospital-name {
                font-size: 24px;
                font-weight: bold;
                color: #0d6efd;
                margin: 0;
            }
            .hospital-address {
                font-size: 14px;
                color: #666;
                margin: 5px 0;
            }
            .document-title {
                font-size: 20px;
                font-weight: bold;
                text-align: center;
                margin: 30px 0;
                color: #0d6efd;
            }
            .registration-number {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 30px;
            }
            .info-section {
                margin-bottom: 25px;
            }
            .section-title {
                font-size: 16px;
                font-weight: bold;
                color: #0d6efd;
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 5px;
                margin-bottom: 15px;
            }
            .info-table {
                width: 100%;
                border-collapse: collapse;
            }
            .info-table td {
                padding: 8px 0;
                vertical-align: top;
            }
            .info-table .label {
                font-weight: bold;
                width: 200px;
                color: #495057;
            }
            .info-table .value {
                color: #212529;
            }
            .examination-list {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin: 10px 0;
            }
            .examination-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid #dee2e6;
            }
            .examination-item:last-child {
                border-bottom: none;
            }
            .total-price {
                font-size: 18px;
                font-weight: bold;
                text-align: right;
                color: #0d6efd;
                margin-top: 15px;
                padding-top: 15px;
                border-top: 2px solid #0d6efd;
            }
            .status-badge {
                display: inline-block;
                padding: 5px 15px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                text-transform: uppercase;
            }
            .status-pending {
                background-color: #fff3cd;
                color: #856404;
            }
            .status-confirmed {
                background-color: #d1ecf1;
                color: #0c5460;
            }
            .status-completed {
                background-color: #d4edda;
                color: #155724;
            }
            .status-cancelled {
                background-color: #f8d7da;
                color: #721c24;
            }
            .footer {
                margin-top: 50px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #dee2e6;
                padding-top: 20px;
            }
            .important-notes {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
            }
            .important-notes h4 {
                color: #856404;
                margin-top: 0;
            }
            .important-notes ul {
                margin: 10px 0;
                padding-left: 20px;
            }
            .important-notes li {
                margin: 5px 0;
                color: #856404;
            }
            @media print {
                body {
                    margin: 0;
                    padding: 15px;
                }
                .header {
                    page-break-inside: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div class="header">
            ${logoExists ? '<img src="data:image/png;base64,' + fs.readFileSync(logoPath, 'base64') + '" alt="Logo" class="logo">' : ''}
            <h1 class="hospital-name">LABORATORIUM KESEHATAN</h1>
            <p class="hospital-address">
                Jl. Kesehatan No. 123, Jakarta<br>
                Telp: (************* | Email: <EMAIL>
            </p>
        </div>

        <div class="document-title">BUKTI REGISTRASI PEMERIKSAAN LABORATORIUM</div>

        <div class="registration-number">
            Nomor Registrasi: ${registration.registration_number}
        </div>

        <div class="info-section">
            <div class="section-title">Data Pasien</div>
            <table class="info-table">
                <tr>
                    <td class="label">NIK:</td>
                    <td class="value">${registration.nik}</td>
                </tr>
                <tr>
                    <td class="label">Nama Lengkap:</td>
                    <td class="value">${registration.full_name}</td>
                </tr>
                <tr>
                    <td class="label">Tanggal Lahir:</td>
                    <td class="value">${formatDateIndonesia(registration.birth_date)}</td>
                </tr>
                <tr>
                    <td class="label">Jenis Kelamin:</td>
                    <td class="value">${registration.gender === 'L' ? 'Laki-laki' : 'Perempuan'}</td>
                </tr>
                <tr>
                    <td class="label">No. Telepon:</td>
                    <td class="value">${registration.phone}</td>
                </tr>
                ${registration.email ? `
                <tr>
                    <td class="label">Email:</td>
                    <td class="value">${registration.email}</td>
                </tr>
                ` : ''}
                ${registration.bpjs_number ? `
                <tr>
                    <td class="label">No. BPJS:</td>
                    <td class="value">${registration.bpjs_number}</td>
                </tr>
                ` : ''}
                <tr>
                    <td class="label">Alamat:</td>
                    <td class="value">${registration.address}</td>
                </tr>
            </table>
        </div>

        <div class="info-section">
            <div class="section-title">Jadwal Pemeriksaan</div>
            <table class="info-table">
                <tr>
                    <td class="label">Tanggal:</td>
                    <td class="value">${formatDateIndonesia(registration.examination_date)}</td>
                </tr>
                <tr>
                    <td class="label">Waktu:</td>
                    <td class="value">${registration.examination_time} WIB</td>
                </tr>
                <tr>
                    <td class="label">Status:</td>
                    <td class="value">
                        <span class="status-badge status-${registration.status}">
                            ${getStatusText(registration.status)}
                        </span>
                    </td>
                </tr>
            </table>
        </div>

        <div class="info-section">
            <div class="section-title">Jenis Pemeriksaan</div>
            <div class="examination-list">
                ${registration.examination_details.split('\n').map(exam => {
                    const [name, price] = exam.split(' - Rp ');
                    return `
                        <div class="examination-item">
                            <span>${name}</span>
                            <span>Rp ${price}</span>
                        </div>
                    `;
                }).join('')}
                <div class="total-price">
                    Total Biaya: Rp ${formatCurrency(registration.total_price)}
                </div>
            </div>
        </div>

        ${registration.notes ? `
        <div class="info-section">
            <div class="section-title">Catatan</div>
            <p>${registration.notes}</p>
        </div>
        ` : ''}

        <div class="important-notes">
            <h4>Informasi Penting:</h4>
            <ul>
                <li>Harap datang 15 menit sebelum waktu pemeriksaan</li>
                <li>Bawa bukti registrasi ini saat datang ke laboratorium</li>
                <li>Untuk pemeriksaan puasa, tidak makan/minum selama 8-12 jam sebelumnya</li>
                <li>Jika berhalangan hadir, hubungi laboratorium minimal 2 jam sebelumnya</li>
                <li>Pembayaran dapat dilakukan di kasir laboratorium</li>
            </ul>
        </div>

        <div class="footer">
            <p>Dokumen ini dicetak pada: ${formatDateIndonesia(new Date())}</p>
            <p>Untuk informasi lebih lanjut, hubungi: (*************</p>
        </div>
    </body>
    </html>
    `;
}

// Generate HTML untuk laporan registrasi
function generateRegistrationReportHTML(registrations, filters = {}) {
    const totalRegistrations = registrations.length;
    const totalRevenue = registrations.reduce((sum, reg) => sum + parseFloat(reg.total_price || 0), 0);
    
    return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Laporan Registrasi Laboratorium</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #333;
                font-size: 12px;
            }
            .header {
                text-align: center;
                border-bottom: 2px solid #0d6efd;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            .report-title {
                font-size: 18px;
                font-weight: bold;
                color: #0d6efd;
                margin: 20px 0;
            }
            .summary {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }
            .summary-item {
                display: inline-block;
                margin-right: 30px;
                font-weight: bold;
            }
            .data-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            .data-table th,
            .data-table td {
                border: 1px solid #dee2e6;
                padding: 8px;
                text-align: left;
            }
            .data-table th {
                background-color: #f8f9fa;
                font-weight: bold;
                color: #495057;
            }
            .data-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .status-badge {
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
                text-transform: uppercase;
            }
            .status-pending { background-color: #fff3cd; color: #856404; }
            .status-confirmed { background-color: #d1ecf1; color: #0c5460; }
            .status-completed { background-color: #d4edda; color: #155724; }
            .status-cancelled { background-color: #f8d7da; color: #721c24; }
            .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #666;
                border-top: 1px solid #dee2e6;
                padding-top: 15px;
            }
            @media print {
                body { margin: 0; padding: 10px; }
                .data-table { font-size: 10px; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>LABORATORIUM KESEHATAN</h1>
            <p>Laporan Registrasi Pemeriksaan</p>
        </div>

        <div class="report-title">
            Laporan Registrasi
            ${filters.date_from && filters.date_to ? 
                `(${formatDateIndonesia(filters.date_from)} - ${formatDateIndonesia(filters.date_to)})` : 
                ''}
        </div>

        <div class="summary">
            <div class="summary-item">Total Registrasi: ${totalRegistrations}</div>
            <div class="summary-item">Total Pendapatan: Rp ${formatCurrency(totalRevenue)}</div>
            <div class="summary-item">Dicetak: ${formatDateIndonesia(new Date())}</div>
        </div>

        <table class="data-table">
            <thead>
                <tr>
                    <th>No</th>
                    <th>No. Registrasi</th>
                    <th>Pasien</th>
                    <th>NIK</th>
                    <th>Tanggal Periksa</th>
                    <th>Waktu</th>
                    <th>Pemeriksaan</th>
                    <th>Status</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                ${registrations.map((reg, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${reg.registration_number}</td>
                        <td>${reg.patient_name}</td>
                        <td>${reg.nik}</td>
                        <td>${formatDateIndonesia(reg.examination_date)}</td>
                        <td>${reg.examination_time}</td>
                        <td style="max-width: 200px; word-wrap: break-word;">${reg.examination_names}</td>
                        <td>
                            <span class="status-badge status-${reg.status}">
                                ${getStatusText(reg.status)}
                            </span>
                        </td>
                        <td>Rp ${formatCurrency(reg.total_price)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div class="footer">
            <p>Laporan ini digenerate secara otomatis pada ${formatDateIndonesia(new Date())}</p>
        </div>
    </body>
    </html>
    `;
}

// Helper function untuk status text
function getStatusText(status) {
    const statusMap = {
        'pending': 'Menunggu',
        'confirmed': 'Dikonfirmasi',
        'completed': 'Selesai',
        'cancelled': 'Dibatalkan'
    };
    return statusMap[status] || status;
}

module.exports = {
    generateRegistrationHTML,
    generateRegistrationReportHTML
};
