/* Custom CSS for Lab Registration System */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

.hero-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-section h1 {
    color: white;
}

.hero-section .btn {
    padding: 12px 30px;
    font-weight: 600;
}

/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: white;
    padding: 0 10px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: center;
    font-weight: 500;
}

.step.active .step-number {
    background-color: var(--primary-color);
    color: white;
}

.step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

.step.completed .step-number {
    background-color: var(--success-color);
    color: white;
}

.step.completed .step-label {
    color: var(--success-color);
}

/* Form Steps */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Examination Grid */
.examination-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.examination-item {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.examination-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.examination-item.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.examination-item .check-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: var(--success-color);
    font-size: 1.2rem;
    display: none;
}

.examination-item.selected .check-icon {
    display: block;
}

.examination-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.examination-description {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.examination-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Selected Examinations */
.selected-examinations-list {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

.selected-examination-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.selected-examination-item:last-child {
    border-bottom: none;
}

.remove-examination {
    color: var(--danger-color);
    cursor: pointer;
    font-size: 1.2rem;
}

.remove-examination:hover {
    color: #b02a37;
}

.total-price {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem;
    border-radius: 0.375rem;
    text-align: center;
    font-size: 1.2rem;
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Registration Status */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
        text-align: center;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 1rem;
    }
    
    .progress-steps::before {
        display: none;
    }
    
    .examination-grid {
        grid-template-columns: 1fr;
        max-height: 300px;
    }
    
    .step-label {
        font-size: 0.75rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Form Validation */
.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-select:valid {
    border-color: var(--success-color);
}

.was-validated .form-select:invalid {
    border-color: var(--danger-color);
}

/* Custom Scrollbar */
.examination-grid::-webkit-scrollbar {
    width: 6px;
}

.examination-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.examination-grid::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.examination-grid::-webkit-scrollbar-thumb:hover {
    background: #0b5ed7;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles */
@media print {
    .navbar, .hero-section, footer {
        display: none;
    }
    
    .container {
        max-width: none;
    }
}
