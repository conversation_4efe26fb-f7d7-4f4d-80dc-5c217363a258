const { body, validationResult } = require('express-validator');

// Middleware untuk handle validation errors
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            error: 'Data tidak valid',
            details: errors.array().map(error => ({
                field: error.path,
                message: error.msg,
                value: error.value
            }))
        });
    }
    next();
};

// Validasi untuk registrasi pasien
const validatePatientRegistration = [
    body('nik')
        .isLength({ min: 16, max: 16 })
        .withMessage('NIK harus 16 digit')
        .isNumeric()
        .withMessage('NIK hanya boleh berisi angka'),
    
    body('full_name')
        .isLength({ min: 2, max: 100 })
        .withMessage('Nama lengkap harus 2-100 karakter')
        .matches(/^[a-zA-Z\s.'-]+$/)
        .withMessage('Nama hanya boleh berisi huruf, spasi, titik, apostrof, dan tanda hubung'),
    
    body('birth_date')
        .isDate()
        .withMessage('Tanggal lahir tidak valid')
        .custom((value) => {
            const birthDate = new Date(value);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            
            if (age < 0 || age > 120) {
                throw new Error('Umur tidak valid');
            }
            return true;
        }),
    
    body('gender')
        .isIn(['L', 'P'])
        .withMessage('Jenis kelamin harus L (Laki-laki) atau P (Perempuan)'),
    
    body('phone')
        .isMobilePhone('id-ID')
        .withMessage('Nomor telepon tidak valid'),
    
    body('email')
        .optional()
        .isEmail()
        .withMessage('Format email tidak valid'),
    
    body('address')
        .isLength({ min: 10, max: 500 })
        .withMessage('Alamat harus 10-500 karakter'),
    
    body('bpjs_number')
        .optional()
        .isLength({ min: 13, max: 13 })
        .withMessage('Nomor BPJS harus 13 digit')
        .isNumeric()
        .withMessage('Nomor BPJS hanya boleh berisi angka'),
    
    body('emergency_contact_name')
        .optional()
        .isLength({ min: 2, max: 100 })
        .withMessage('Nama kontak darurat harus 2-100 karakter'),
    
    body('emergency_contact_phone')
        .optional()
        .isMobilePhone('id-ID')
        .withMessage('Nomor kontak darurat tidak valid'),
    
    handleValidationErrors
];

// Validasi untuk pendaftaran pemeriksaan
const validateExaminationRegistration = [
    body('patient_id')
        .isInt({ min: 1 })
        .withMessage('ID pasien tidak valid'),
    
    body('examination_date')
        .isDate()
        .withMessage('Tanggal pemeriksaan tidak valid')
        .custom((value) => {
            const examDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (examDate < today) {
                throw new Error('Tanggal pemeriksaan tidak boleh di masa lalu');
            }
            
            // Maksimal 30 hari ke depan
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 30);
            
            if (examDate > maxDate) {
                throw new Error('Tanggal pemeriksaan maksimal 30 hari ke depan');
            }
            
            return true;
        }),
    
    body('examination_time')
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Format waktu tidak valid (HH:MM)')
        .custom((value) => {
            const [hours, minutes] = value.split(':').map(Number);
            
            // Jam operasional 07:00 - 16:00
            if (hours < 7 || hours > 16 || (hours === 16 && minutes > 0)) {
                throw new Error('Waktu pemeriksaan harus antara 07:00 - 16:00');
            }
            
            return true;
        }),
    
    body('examination_types')
        .isArray({ min: 1 })
        .withMessage('Minimal pilih 1 jenis pemeriksaan'),
    
    body('examination_types.*')
        .isInt({ min: 1 })
        .withMessage('ID jenis pemeriksaan tidak valid'),
    
    body('notes')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Catatan maksimal 1000 karakter'),
    
    handleValidationErrors
];

// Validasi untuk login admin
const validateAdminLogin = [
    body('username')
        .isLength({ min: 3, max: 50 })
        .withMessage('Username harus 3-50 karakter')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('Username hanya boleh berisi huruf, angka, dan underscore'),
    
    body('password')
        .isLength({ min: 6 })
        .withMessage('Password minimal 6 karakter'),
    
    handleValidationErrors
];

// Validasi untuk update status registrasi
const validateRegistrationStatus = [
    body('status')
        .isIn(['pending', 'confirmed', 'completed', 'cancelled'])
        .withMessage('Status tidak valid'),
    
    body('notes')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Catatan maksimal 1000 karakter'),
    
    handleValidationErrors
];

// Sanitasi input untuk mencegah XSS
const sanitizeInput = (req, res, next) => {
    const sanitize = (obj) => {
        for (let key in obj) {
            if (typeof obj[key] === 'string') {
                obj[key] = obj[key]
                    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                    .replace(/<[^>]*>/g, '')
                    .trim();
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitize(obj[key]);
            }
        }
    };
    
    if (req.body) sanitize(req.body);
    if (req.query) sanitize(req.query);
    if (req.params) sanitize(req.params);
    
    next();
};

module.exports = {
    validatePatientRegistration,
    validateExaminationRegistration,
    validateAdminLogin,
    validateRegistrationStatus,
    sanitizeInput,
    handleValidationErrors
};
