const nodemailer = require('nodemailer');
const { formatDateIndonesia, formatCurrency } = require('../models/database');

// Konfigurasi transporter email
let transporter = null;

// Inisialisasi email transporter
function initializeEmailService() {
    if (!process.env.EMAIL_HOST || !process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        console.warn('Email configuration not found. Email notifications will be disabled.');
        return false;
    }

    try {
        transporter = nodemailer.createTransporter({
            host: process.env.EMAIL_HOST,
            port: process.env.EMAIL_PORT || 587,
            secure: false, // true for 465, false for other ports
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS
            },
            tls: {
                rejectUnauthorized: false
            }
        });

        // Verify connection
        transporter.verify((error, success) => {
            if (error) {
                console.error('Email service verification failed:', error);
                transporter = null;
            } else {
                console.log('✅ Email service ready');
            }
        });

        return true;
    } catch (error) {
        console.error('Failed to initialize email service:', error);
        return false;
    }
}

// Template email untuk konfirmasi registrasi
function getRegistrationConfirmationTemplate(registration) {
    return {
        subject: `Konfirmasi Registrasi Laboratorium - ${registration.registration_number}`,
        html: `
        <!DOCTYPE html>
        <html lang="id">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Konfirmasi Registrasi</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    background-color: #0d6efd;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 8px 8px 0 0;
                }
                .content {
                    background-color: #f8f9fa;
                    padding: 30px;
                    border-radius: 0 0 8px 8px;
                }
                .registration-card {
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .registration-number {
                    font-size: 24px;
                    font-weight: bold;
                    color: #0d6efd;
                    text-align: center;
                    margin-bottom: 20px;
                    padding: 15px;
                    background-color: #e7f3ff;
                    border-radius: 8px;
                }
                .info-row {
                    display: flex;
                    justify-content: space-between;
                    padding: 8px 0;
                    border-bottom: 1px solid #dee2e6;
                }
                .info-row:last-child {
                    border-bottom: none;
                }
                .label {
                    font-weight: bold;
                    color: #495057;
                }
                .value {
                    color: #212529;
                }
                .examination-list {
                    background-color: #e7f3ff;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 15px 0;
                }
                .examination-item {
                    padding: 5px 0;
                    border-bottom: 1px solid #b3d9ff;
                }
                .examination-item:last-child {
                    border-bottom: none;
                }
                .total-price {
                    font-size: 18px;
                    font-weight: bold;
                    color: #0d6efd;
                    text-align: center;
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 2px solid #0d6efd;
                }
                .important-info {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 20px 0;
                }
                .important-info h3 {
                    color: #856404;
                    margin-top: 0;
                }
                .important-info ul {
                    margin: 10px 0;
                    padding-left: 20px;
                }
                .important-info li {
                    margin: 5px 0;
                    color: #856404;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #dee2e6;
                    color: #6c757d;
                    font-size: 14px;
                }
                .button {
                    display: inline-block;
                    background-color: #0d6efd;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 6px;
                    font-weight: bold;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>LABORATORIUM KESEHATAN</h1>
                <p>Konfirmasi Registrasi Pemeriksaan</p>
            </div>
            
            <div class="content">
                <h2>Halo ${registration.full_name},</h2>
                <p>Terima kasih telah mendaftar pemeriksaan laboratorium. Berikut adalah detail registrasi Anda:</p>
                
                <div class="registration-card">
                    <div class="registration-number">
                        ${registration.registration_number}
                    </div>
                    
                    <div class="info-row">
                        <span class="label">Nama Pasien:</span>
                        <span class="value">${registration.full_name}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">NIK:</span>
                        <span class="value">${registration.nik}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Tanggal Pemeriksaan:</span>
                        <span class="value">${formatDateIndonesia(registration.examination_date)}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Waktu:</span>
                        <span class="value">${registration.examination_time} WIB</span>
                    </div>
                    <div class="info-row">
                        <span class="label">No. Telepon:</span>
                        <span class="value">${registration.phone}</span>
                    </div>
                </div>
                
                <h3>Jenis Pemeriksaan:</h3>
                <div class="examination-list">
                    ${registration.examination_details.split('\n').map(exam => {
                        return `<div class="examination-item">${exam}</div>`;
                    }).join('')}
                    <div class="total-price">
                        Total Biaya: Rp ${formatCurrency(registration.total_price)}
                    </div>
                </div>
                
                <div class="important-info">
                    <h3>Informasi Penting:</h3>
                    <ul>
                        <li>Harap datang 15 menit sebelum waktu pemeriksaan</li>
                        <li>Bawa bukti registrasi ini saat datang ke laboratorium</li>
                        <li>Untuk pemeriksaan puasa, tidak makan/minum selama 8-12 jam sebelumnya</li>
                        <li>Jika berhalangan hadir, hubungi laboratorium minimal 2 jam sebelumnya</li>
                        <li>Pembayaran dapat dilakukan di kasir laboratorium</li>
                    </ul>
                </div>
                
                <div style="text-align: center;">
                    <a href="http://localhost:3000/#check-registration" class="button">
                        Cek Status Registrasi
                    </a>
                </div>
            </div>
            
            <div class="footer">
                <p><strong>LABORATORIUM KESEHATAN</strong></p>
                <p>Jl. Kesehatan No. 123, Jakarta</p>
                <p>Telp: (************* | Email: <EMAIL></p>
                <p>Jam Operasional: Senin - Sabtu, 07:00 - 16:00 WIB</p>
            </div>
        </body>
        </html>
        `,
        text: `
        Konfirmasi Registrasi Laboratorium
        
        Halo ${registration.full_name},
        
        Terima kasih telah mendaftar pemeriksaan laboratorium.
        
        Detail Registrasi:
        - Nomor Registrasi: ${registration.registration_number}
        - Nama: ${registration.full_name}
        - NIK: ${registration.nik}
        - Tanggal: ${formatDateIndonesia(registration.examination_date)}
        - Waktu: ${registration.examination_time} WIB
        - Total Biaya: Rp ${formatCurrency(registration.total_price)}
        
        Pemeriksaan:
        ${registration.examination_details.split('\n').join('\n')}
        
        Informasi Penting:
        - Harap datang 15 menit sebelum waktu pemeriksaan
        - Bawa bukti registrasi ini saat datang ke laboratorium
        - Untuk pemeriksaan puasa, tidak makan/minum selama 8-12 jam sebelumnya
        
        Hubungi kami: (*************
        
        LABORATORIUM KESEHATAN
        Jl. Kesehatan No. 123, Jakarta
        `
    };
}

// Template email untuk update status
function getStatusUpdateTemplate(registration, oldStatus, newStatus) {
    const statusText = {
        'pending': 'Menunggu Konfirmasi',
        'confirmed': 'Dikonfirmasi',
        'completed': 'Selesai',
        'cancelled': 'Dibatalkan'
    };

    return {
        subject: `Update Status Registrasi - ${registration.registration_number}`,
        html: `
        <!DOCTYPE html>
        <html lang="id">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Update Status Registrasi</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    background-color: #0d6efd;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 8px 8px 0 0;
                }
                .content {
                    background-color: #f8f9fa;
                    padding: 30px;
                    border-radius: 0 0 8px 8px;
                }
                .status-update {
                    background-color: white;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                    text-align: center;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .status-badge {
                    display: inline-block;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-weight: bold;
                    margin: 0 10px;
                }
                .status-confirmed {
                    background-color: #d1ecf1;
                    color: #0c5460;
                }
                .status-completed {
                    background-color: #d4edda;
                    color: #155724;
                }
                .status-cancelled {
                    background-color: #f8d7da;
                    color: #721c24;
                }
                .arrow {
                    font-size: 24px;
                    color: #0d6efd;
                    margin: 0 10px;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #dee2e6;
                    color: #6c757d;
                    font-size: 14px;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>LABORATORIUM KESEHATAN</h1>
                <p>Update Status Registrasi</p>
            </div>
            
            <div class="content">
                <h2>Halo ${registration.patient_name},</h2>
                <p>Status registrasi Anda telah diperbarui:</p>
                
                <div class="status-update">
                    <h3>Nomor Registrasi: ${registration.registration_number}</h3>
                    <div style="margin: 20px 0;">
                        <span class="status-badge status-${oldStatus}">${statusText[oldStatus]}</span>
                        <span class="arrow">→</span>
                        <span class="status-badge status-${newStatus}">${statusText[newStatus]}</span>
                    </div>
                    <p><strong>Status Terbaru: ${statusText[newStatus]}</strong></p>
                </div>
                
                ${newStatus === 'confirmed' ? `
                <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4 style="color: #155724; margin-top: 0;">Registrasi Dikonfirmasi!</h4>
                    <p style="color: #155724;">
                        Silakan datang sesuai jadwal yang telah ditentukan:<br>
                        <strong>${formatDateIndonesia(registration.examination_date)} - ${registration.examination_time} WIB</strong>
                    </p>
                </div>
                ` : ''}
                
                ${newStatus === 'completed' ? `
                <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4 style="color: #155724; margin-top: 0;">Pemeriksaan Selesai!</h4>
                    <p style="color: #155724;">
                        Terima kasih telah menggunakan layanan kami. 
                        Hasil pemeriksaan dapat diambil sesuai dengan ketentuan laboratorium.
                    </p>
                </div>
                ` : ''}
                
                ${newStatus === 'cancelled' ? `
                <div style="background-color: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4 style="color: #721c24; margin-top: 0;">Registrasi Dibatalkan</h4>
                    <p style="color: #721c24;">
                        Registrasi Anda telah dibatalkan. 
                        Jika ada pertanyaan, silakan hubungi laboratorium.
                    </p>
                </div>
                ` : ''}
                
                ${registration.notes ? `
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h4 style="color: #856404; margin-top: 0;">Catatan:</h4>
                    <p style="color: #856404;">${registration.notes}</p>
                </div>
                ` : ''}
            </div>
            
            <div class="footer">
                <p><strong>LABORATORIUM KESEHATAN</strong></p>
                <p>Jl. Kesehatan No. 123, Jakarta</p>
                <p>Telp: (************* | Email: <EMAIL></p>
            </div>
        </body>
        </html>
        `,
        text: `
        Update Status Registrasi
        
        Halo ${registration.patient_name},
        
        Status registrasi ${registration.registration_number} telah diperbarui:
        ${statusText[oldStatus]} → ${statusText[newStatus]}
        
        ${newStatus === 'confirmed' ? `
        Registrasi dikonfirmasi! Silakan datang pada:
        ${formatDateIndonesia(registration.examination_date)} - ${registration.examination_time} WIB
        ` : ''}
        
        ${registration.notes ? `Catatan: ${registration.notes}` : ''}
        
        Hubungi kami: (*************
        
        LABORATORIUM KESEHATAN
        `
    };
}

// Kirim email konfirmasi registrasi
async function sendRegistrationConfirmation(registration) {
    if (!transporter) {
        console.warn('Email service not available');
        return false;
    }

    if (!registration.email) {
        console.warn('No email address provided for registration:', registration.registration_number);
        return false;
    }

    try {
        const template = getRegistrationConfirmationTemplate(registration);
        
        const mailOptions = {
            from: `"Laboratorium Kesehatan" <${process.env.EMAIL_USER}>`,
            to: registration.email,
            subject: template.subject,
            html: template.html,
            text: template.text
        };

        const result = await transporter.sendMail(mailOptions);
        console.log('Registration confirmation email sent:', result.messageId);
        return true;
    } catch (error) {
        console.error('Failed to send registration confirmation email:', error);
        return false;
    }
}

// Kirim email update status
async function sendStatusUpdate(registration, oldStatus, newStatus) {
    if (!transporter) {
        console.warn('Email service not available');
        return false;
    }

    if (!registration.email) {
        console.warn('No email address provided for registration:', registration.registration_number);
        return false;
    }

    try {
        const template = getStatusUpdateTemplate(registration, oldStatus, newStatus);
        
        const mailOptions = {
            from: `"Laboratorium Kesehatan" <${process.env.EMAIL_USER}>`,
            to: registration.email,
            subject: template.subject,
            html: template.html,
            text: template.text
        };

        const result = await transporter.sendMail(mailOptions);
        console.log('Status update email sent:', result.messageId);
        return true;
    } catch (error) {
        console.error('Failed to send status update email:', error);
        return false;
    }
}

// Inisialisasi service saat module dimuat
initializeEmailService();

module.exports = {
    initializeEmailService,
    sendRegistrationConfirmation,
    sendStatusUpdate
};
