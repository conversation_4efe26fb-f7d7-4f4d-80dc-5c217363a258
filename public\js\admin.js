// Admin Dashboard JavaScript

// Global variables
let authToken = null;
let currentAdmin = null;
let currentPage = 1;
let currentFilters = {};
let registrationChart = null;

// API Base URL
const API_BASE_URL = '/api';

// Initialize admin app
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    setupEventListeners();
});

// Check authentication status
function checkAuthStatus() {
    authToken = localStorage.getItem('admin_token');
    
    if (!authToken) {
        showLoginModal();
    } else {
        // Verify token validity
        verifyToken();
    }
}

// Verify token with server
async function verifyToken() {
    try {
        const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            initializeDashboard();
        } else {
            localStorage.removeItem('admin_token');
            showLoginModal();
        }
    } catch (error) {
        console.error('Token verification error:', error);
        showLoginModal();
    }
}

// Show login modal
function showLoginModal() {
    const modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
}

// Setup event listeners
function setupEventListeners() {
    // Login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // Sidebar navigation
    document.querySelectorAll('.menu-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.dataset.section;
            showSection(section);
            
            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(mi => mi.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Filter form
    document.getElementById('filter-status').addEventListener('change', applyFilters);
    document.getElementById('filter-date-from').addEventListener('change', applyFilters);
    document.getElementById('filter-date-to').addEventListener('change', applyFilters);
    document.getElementById('filter-search').addEventListener('input', debounce(applyFilters, 500));
    
    // Patient search
    document.getElementById('patient-search').addEventListener('input', debounce(searchPatients, 500));
}

// Handle admin login
async function handleLogin(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const loginData = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        showLoading();
        
        const response = await fetch(`${API_BASE_URL}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            authToken = data.token;
            currentAdmin = data.admin;
            localStorage.setItem('admin_token', authToken);
            
            // Hide login modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            modal.hide();
            
            // Initialize dashboard
            initializeDashboard();
            
            showAlert('success', 'Login berhasil');
        } else {
            showAlert('error', data.error || 'Login gagal');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('error', 'Terjadi kesalahan saat login');
    } finally {
        hideLoading();
    }
}

// Initialize dashboard
function initializeDashboard() {
    // Update admin name in navbar
    document.getElementById('admin-name').textContent = currentAdmin?.full_name || 'Admin';
    
    // Load dashboard data
    loadDashboardData();
    
    // Show dashboard section
    showSection('dashboard');
}

// Show specific section
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionName).classList.add('active');
    
    // Load section-specific data
    switch (sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'registrations':
            loadRegistrations();
            break;
        case 'patients':
            loadPatients();
            break;
        case 'reports':
            loadReports();
            break;
    }
}

// Load dashboard data
async function loadDashboardData() {
    try {
        showLoading();
        
        const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            updateDashboardStats(data);
            updateRecentRegistrations(data.recent_registrations);
            updatePopularExaminations(data.popular_examinations);
        } else {
            showAlert('error', 'Gagal memuat data dashboard');
        }
    } catch (error) {
        console.error('Dashboard data error:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat dashboard');
    } finally {
        hideLoading();
    }
}

// Update dashboard statistics
function updateDashboardStats(data) {
    document.getElementById('today-registrations').textContent = data.today.total_registrations || 0;
    document.getElementById('completed-registrations').textContent = data.today.completed_registrations || 0;
    document.getElementById('pending-registrations').textContent = data.today.pending_registrations || 0;
    document.getElementById('today-revenue').textContent = formatCurrency(data.today.total_revenue || 0);
}

// Update recent registrations table
function updateRecentRegistrations(registrations) {
    const tbody = document.getElementById('recent-registrations');
    tbody.innerHTML = '';
    
    if (!registrations || registrations.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Tidak ada data</td></tr>';
        return;
    }
    
    registrations.forEach(reg => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${reg.registration_number}</td>
            <td>
                <strong>${reg.patient_name}</strong><br>
                <small class="text-muted">${reg.phone}</small>
            </td>
            <td>${formatDate(reg.examination_date)}</td>
            <td><span class="status-badge status-${reg.status}">${getStatusText(reg.status)}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRegistrationDetail(${reg.id})">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="updateRegistrationStatus(${reg.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Update popular examinations
function updatePopularExaminations(examinations) {
    const container = document.getElementById('popular-examinations');
    container.innerHTML = '';
    
    if (!examinations || examinations.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">Tidak ada data</p>';
        return;
    }
    
    examinations.forEach(exam => {
        const div = document.createElement('div');
        div.className = 'popular-exam-item';
        div.innerHTML = `
            <div>
                <div class="popular-exam-name">${exam.name}</div>
                <small class="text-muted">Rp ${formatCurrency(exam.total_revenue || 0)}</small>
            </div>
            <span class="popular-exam-count">${exam.total_orders || 0}</span>
        `;
        container.appendChild(div);
    });
}

// Load registrations with filters
async function loadRegistrations(page = 1) {
    try {
        showLoading();
        
        const params = new URLSearchParams({
            page: page,
            limit: 20,
            ...currentFilters
        });
        
        const response = await fetch(`${API_BASE_URL}/admin/registrations?${params}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            updateRegistrationsTable(data.registrations);
            updatePagination(data.pagination);
            currentPage = page;
        } else {
            showAlert('error', 'Gagal memuat data registrasi');
        }
    } catch (error) {
        console.error('Load registrations error:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat registrasi');
    } finally {
        hideLoading();
    }
}

// Show registration detail modal
function showRegistrationDetailModal(registration) {
    const content = document.getElementById('registration-detail-content');

    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Data Pasien</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>NIK:</strong></td><td>${registration.nik}</td></tr>
                    <tr><td><strong>Nama:</strong></td><td>${registration.full_name}</td></tr>
                    <tr><td><strong>Tanggal Lahir:</strong></td><td>${formatDate(registration.birth_date)}</td></tr>
                    <tr><td><strong>Jenis Kelamin:</strong></td><td>${registration.gender === 'L' ? 'Laki-laki' : 'Perempuan'}</td></tr>
                    <tr><td><strong>Telepon:</strong></td><td>${registration.phone}</td></tr>
                    <tr><td><strong>Email:</strong></td><td>${registration.email || '-'}</td></tr>
                    <tr><td><strong>BPJS:</strong></td><td>${registration.bpjs_number || '-'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Data Registrasi</h6>
                <table class="table table-borderless table-sm">
                    <tr><td><strong>No. Registrasi:</strong></td><td>${registration.registration_number}</td></tr>
                    <tr><td><strong>Tanggal Periksa:</strong></td><td>${formatDate(registration.examination_date)}</td></tr>
                    <tr><td><strong>Waktu:</strong></td><td>${registration.examination_time}</td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="status-badge status-${registration.status}">${getStatusText(registration.status)}</span></td></tr>
                    <tr><td><strong>Total Biaya:</strong></td><td class="fw-bold">Rp ${formatCurrency(registration.total_price)}</td></tr>
                    <tr><td><strong>Status Bayar:</strong></td><td>${registration.payment_status}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6>Pemeriksaan</h6>
                <div class="card bg-light">
                    <div class="card-body">
                        ${registration.examination_details.split('\n').map(exam => `<div>${exam}</div>`).join('')}
                    </div>
                </div>
            </div>
        </div>

        ${registration.address ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>Alamat</h6>
                <p>${registration.address}</p>
            </div>
        </div>
        ` : ''}

        ${registration.notes ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>Catatan</h6>
                <div class="alert alert-info">${registration.notes}</div>
            </div>
        </div>
        ` : ''}

        ${registration.documents && registration.documents.length > 0 ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>Dokumen</h6>
                <div class="list-group">
                    ${registration.documents.map(doc => `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${doc.original_name}</strong><br>
                                <small class="text-muted">${doc.document_type}</small>
                            </div>
                            <a href="/uploads/documents/${doc.file_name}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-download"></i>
                            </a>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
        ` : ''}
    `;

    const modal = new bootstrap.Modal(document.getElementById('registrationDetailModal'));
    modal.show();
}

// Update registration status
function updateRegistrationStatus(registrationId) {
    document.getElementById('update-registration-id').value = registrationId;

    const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
    modal.show();
}

// Save status update
async function saveStatusUpdate() {
    const registrationId = document.getElementById('update-registration-id').value;
    const status = document.getElementById('update-status').value;
    const notes = document.getElementById('update-notes').value;

    try {
        showLoading();

        const response = await fetch(`${API_BASE_URL}/admin/registrations/${registrationId}/status`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status, notes })
        });

        const data = await response.json();

        if (response.ok) {
            showAlert('success', 'Status berhasil diperbarui');

            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('updateStatusModal'));
            modal.hide();

            // Refresh current view
            const activeSection = document.querySelector('.content-section.active').id;
            if (activeSection === 'dashboard') {
                loadDashboardData();
            } else if (activeSection === 'registrations') {
                loadRegistrations(currentPage);
            }
        } else {
            showAlert('error', data.error || 'Gagal memperbarui status');
        }
    } catch (error) {
        console.error('Update status error:', error);
        showAlert('error', 'Terjadi kesalahan saat memperbarui status');
    } finally {
        hideLoading();
    }
}

// Load patients
async function loadPatients() {
    try {
        showLoading();

        const response = await fetch(`${API_BASE_URL}/patients`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        const data = await response.json();

        if (response.ok) {
            updatePatientsTable(data.patients || []);
        } else {
            showAlert('error', 'Gagal memuat data pasien');
        }
    } catch (error) {
        console.error('Load patients error:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat data pasien');
    } finally {
        hideLoading();
    }
}

// Update patients table
function updatePatientsTable(patients) {
    const tbody = document.getElementById('patients-table');
    tbody.innerHTML = '';

    if (!patients || patients.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Tidak ada data</td></tr>';
        return;
    }

    patients.forEach(patient => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${patient.nik}</td>
            <td>${patient.full_name}</td>
            <td>${formatDate(patient.birth_date)}</td>
            <td>${patient.gender === 'L' ? 'Laki-laki' : 'Perempuan'}</td>
            <td>${patient.phone}</td>
            <td>${patient.email || '-'}</td>
            <td>${patient.total_registrations || 0}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewPatientDetail(${patient.id})">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Search patients
async function searchPatients() {
    const query = document.getElementById('patient-search').value;

    if (query.length < 3) {
        loadPatients();
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/patients/search/${encodeURIComponent(query)}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        const data = await response.json();

        if (response.ok) {
            updatePatientsTable(data.patients || []);
        } else {
            showAlert('error', 'Gagal mencari pasien');
        }
    } catch (error) {
        console.error('Search patients error:', error);
        showAlert('error', 'Terjadi kesalahan saat mencari pasien');
    }
}

// Load reports
async function loadReports() {
    try {
        showLoading();

        const period = document.getElementById('report-period').value;
        const response = await fetch(`${API_BASE_URL}/admin/reports/statistics?period=${period}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        const data = await response.json();

        if (response.ok) {
            updateReportsChart(data.statistics);
            updateStatisticsSummary(data.statistics);
        } else {
            showAlert('error', 'Gagal memuat laporan');
        }
    } catch (error) {
        console.error('Load reports error:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat laporan');
    } finally {
        hideLoading();
    }
}

// Update reports chart
function updateReportsChart(statistics) {
    const ctx = document.getElementById('registrationChart').getContext('2d');

    if (registrationChart) {
        registrationChart.destroy();
    }

    const labels = statistics.map(stat => {
        const period = document.getElementById('report-period').value;
        if (period === 'monthly') {
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];
            return months[stat.period - 1];
        }
        return stat.period;
    });

    registrationChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Total Registrasi',
                data: statistics.map(stat => stat.total_registrations),
                borderColor: 'rgb(13, 110, 253)',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.1
            }, {
                label: 'Selesai',
                data: statistics.map(stat => stat.completed_registrations),
                borderColor: 'rgb(25, 135, 84)',
                backgroundColor: 'rgba(25, 135, 84, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Statistik Registrasi'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Update statistics summary
function updateStatisticsSummary(statistics) {
    const container = document.getElementById('statistics-summary');

    const totalRegistrations = statistics.reduce((sum, stat) => sum + stat.total_registrations, 0);
    const totalCompleted = statistics.reduce((sum, stat) => sum + stat.completed_registrations, 0);
    const totalRevenue = statistics.reduce((sum, stat) => sum + (stat.total_revenue || 0), 0);
    const uniquePatients = statistics.reduce((sum, stat) => sum + (stat.unique_patients || 0), 0);

    container.innerHTML = `
        <div class="stat-summary-item">
            <span class="stat-summary-label">Total Registrasi</span>
            <span class="stat-summary-value">${totalRegistrations}</span>
        </div>
        <div class="stat-summary-item">
            <span class="stat-summary-label">Selesai</span>
            <span class="stat-summary-value">${totalCompleted}</span>
        </div>
        <div class="stat-summary-item">
            <span class="stat-summary-label">Total Pendapatan</span>
            <span class="stat-summary-value">Rp ${formatCurrency(totalRevenue)}</span>
        </div>
        <div class="stat-summary-item">
            <span class="stat-summary-label">Pasien Unik</span>
            <span class="stat-summary-value">${uniquePatients}</span>
        </div>
    `;
}

// Utility functions
function getStatusText(status) {
    const statusMap = {
        'pending': 'Menunggu',
        'confirmed': 'Dikonfirmasi',
        'completed': 'Selesai',
        'cancelled': 'Dibatalkan'
    };
    return statusMap[status] || status;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('id-ID').format(amount);
}

function formatDate(dateString) {
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: 'Asia/Jakarta'
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showLoading() {
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
    if (modal) {
        modal.hide();
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Refresh functions
function refreshDashboard() {
    loadDashboardData();
}

function refreshRegistrations() {
    loadRegistrations(currentPage);
}

function generateReport() {
    loadReports();
}

// Export functions
function exportRegistrations() {
    showAlert('info', 'Fitur export akan segera tersedia');
}

function exportPatients() {
    showAlert('info', 'Fitur export akan segera tersedia');
}

// Print functions
function printRegistration(registrationId) {
    window.open(`/print-registration/${registrationId}`, '_blank');
}

function printRegistrationDetail() {
    window.print();
}

// Logout function
function logout() {
    localStorage.removeItem('admin_token');
    authToken = null;
    currentAdmin = null;
    showLoginModal();
    showAlert('info', 'Anda telah logout');
}

// Update registrations table
function updateRegistrationsTable(registrations) {
    const tbody = document.getElementById('registrations-table');
    tbody.innerHTML = '';
    
    if (!registrations || registrations.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">Tidak ada data</td></tr>';
        return;
    }
    
    registrations.forEach(reg => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${reg.registration_number}</td>
            <td>${reg.patient_name}</td>
            <td>${reg.nik}</td>
            <td>${reg.phone}</td>
            <td>${formatDate(reg.examination_date)}</td>
            <td>${reg.examination_time}</td>
            <td class="text-truncate-2" title="${reg.examination_names}">${reg.examination_names}</td>
            <td><span class="status-badge status-${reg.status}">${getStatusText(reg.status)}</span></td>
            <td class="fw-bold">Rp ${formatCurrency(reg.total_price)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRegistrationDetail(${reg.id})" title="Lihat Detail">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="updateRegistrationStatus(${reg.id})" title="Update Status">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="printRegistration(${reg.id})" title="Cetak">
                        <i class="bi bi-printer"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Update pagination
function updatePagination(pagination) {
    const container = document.getElementById('pagination');
    container.innerHTML = '';
    
    if (pagination.total_pages <= 1) return;
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.current_page === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page - 1})">Previous</a>`;
    container.appendChild(prevLi);
    
    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.current_page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadRegistrations(${i})">${i}</a>`;
        container.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadRegistrations(${pagination.current_page + 1})">Next</a>`;
    container.appendChild(nextLi);
}

// Apply filters
function applyFilters() {
    currentFilters = {
        status: document.getElementById('filter-status').value,
        date_from: document.getElementById('filter-date-from').value,
        date_to: document.getElementById('filter-date-to').value,
        search: document.getElementById('filter-search').value
    };
    
    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });
    
    loadRegistrations(1);
}

// Clear filters
function clearFilters() {
    document.getElementById('filter-status').value = '';
    document.getElementById('filter-date-from').value = '';
    document.getElementById('filter-date-to').value = '';
    document.getElementById('filter-search').value = '';
    
    currentFilters = {};
    loadRegistrations(1);
}

// View registration detail
async function viewRegistrationDetail(registrationId) {
    try {
        showLoading();
        
        const response = await fetch(`${API_BASE_URL}/admin/registrations/${registrationId}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showRegistrationDetailModal(data.registration);
        } else {
            showAlert('error', 'Gagal memuat detail registrasi');
        }
    } catch (error) {
        console.error('View registration detail error:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat detail');
    } finally {
        hideLoading();
    }
}
