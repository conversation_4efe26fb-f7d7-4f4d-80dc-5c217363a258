<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pendaftaran Online Laboratorium</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-hospital"></i>
                Lab Online
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Layanan</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#registration">Daftar</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#check-registration">Cek Status</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold text-primary mb-4">
                        Pendaftaran Online Laboratorium
                    </h1>
                    <p class="lead mb-4">
                        Daftar pemeriksaan laboratorium dengan mudah dan cepat. 
                        Sistem online 24/7 untuk kemudahan Anda.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#registration" class="btn btn-primary btn-lg">
                            <i class="bi bi-calendar-plus"></i> Daftar Sekarang
                        </a>
                        <a href="#check-registration" class="btn btn-outline-primary btn-lg">
                            <i class="bi bi-search"></i> Cek Status
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <img src="images/lab-hero.jpg" alt="Laboratorium" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold">Layanan Pemeriksaan</h2>
                    <p class="lead">Berbagai jenis pemeriksaan laboratorium tersedia</p>
                </div>
            </div>
            <div class="row" id="examination-types">
                <!-- Examination types will be loaded here -->
            </div>
        </div>
    </section>

    <!-- Registration Section -->
    <section id="registration" class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="bi bi-person-plus"></i>
                                Form Pendaftaran Pemeriksaan
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Progress Steps -->
                            <div class="progress-steps mb-4">
                                <div class="step active" data-step="1">
                                    <div class="step-number">1</div>
                                    <div class="step-label">Data Pasien</div>
                                </div>
                                <div class="step" data-step="2">
                                    <div class="step-number">2</div>
                                    <div class="step-label">Pilih Pemeriksaan</div>
                                </div>
                                <div class="step" data-step="3">
                                    <div class="step-number">3</div>
                                    <div class="step-label">Jadwal & Konfirmasi</div>
                                </div>
                            </div>

                            <!-- Registration Form -->
                            <form id="registrationForm" novalidate>
                                <!-- Step 1: Patient Data -->
                                <div class="form-step active" data-step="1">
                                    <h5 class="mb-3">Data Pasien</h5>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="nik" class="form-label">NIK <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="nik" name="nik" 
                                                   maxlength="16" pattern="[0-9]{16}" required>
                                            <div class="invalid-feedback">NIK harus 16 digit angka</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="full_name" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                                            <div class="invalid-feedback">Nama lengkap wajib diisi</div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="birth_date" class="form-label">Tanggal Lahir <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                            <div class="invalid-feedback">Tanggal lahir wajib diisi</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="gender" class="form-label">Jenis Kelamin <span class="text-danger">*</span></label>
                                            <select class="form-select" id="gender" name="gender" required>
                                                <option value="">Pilih Jenis Kelamin</option>
                                                <option value="L">Laki-laki</option>
                                                <option value="P">Perempuan</option>
                                            </select>
                                            <div class="invalid-feedback">Jenis kelamin wajib dipilih</div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">No. Telepon <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   pattern="[0-9]{10,13}" required>
                                            <div class="invalid-feedback">Nomor telepon tidak valid</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" name="email">
                                            <div class="invalid-feedback">Format email tidak valid</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="address" class="form-label">Alamat Lengkap <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                                        <div class="invalid-feedback">Alamat lengkap wajib diisi</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="bpjs_number" class="form-label">No. BPJS (Opsional)</label>
                                            <input type="text" class="form-control" id="bpjs_number" name="bpjs_number" 
                                                   maxlength="13" pattern="[0-9]{13}">
                                            <div class="invalid-feedback">Nomor BPJS harus 13 digit</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="emergency_contact_name" class="form-label">Kontak Darurat</label>
                                            <input type="text" class="form-control" id="emergency_contact_name" 
                                                   name="emergency_contact_name" placeholder="Nama">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="emergency_contact_phone" class="form-label">No. Telepon Kontak Darurat</label>
                                        <input type="tel" class="form-control" id="emergency_contact_phone" 
                                               name="emergency_contact_phone" pattern="[0-9]{10,13}">
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                                            Selanjutnya <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Step 2: Examination Selection -->
                                <div class="form-step" data-step="2">
                                    <h5 class="mb-3">Pilih Jenis Pemeriksaan</h5>
                                    
                                    <!-- Search and Filter -->
                                    <div class="row mb-3">
                                        <div class="col-md-8">
                                            <input type="text" class="form-control" id="examination-search" 
                                                   placeholder="Cari jenis pemeriksaan...">
                                        </div>
                                        <div class="col-md-4">
                                            <select class="form-select" id="examination-category">
                                                <option value="">Semua Kategori</option>
                                                <option value="darah">Pemeriksaan Darah</option>
                                                <option value="urine">Pemeriksaan Urine</option>
                                                <option value="fungsi-organ">Fungsi Organ</option>
                                                <option value="infeksi">Pemeriksaan Infeksi</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Examination List -->
                                    <div id="examination-list" class="examination-grid">
                                        <!-- Examinations will be loaded here -->
                                    </div>

                                    <!-- Selected Examinations Summary -->
                                    <div id="selected-examinations" class="mt-4" style="display: none;">
                                        <h6>Pemeriksaan Terpilih:</h6>
                                        <div id="selected-list" class="selected-examinations-list"></div>
                                        <div class="total-price mt-2">
                                            <strong>Total: <span id="total-price">Rp 0</span></strong>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-secondary" onclick="prevStep()">
                                            <i class="bi bi-arrow-left"></i> Sebelumnya
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep()" id="next-to-schedule" disabled>
                                            Selanjutnya <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Step 3: Schedule and Confirmation -->
                                <div class="form-step" data-step="3">
                                    <h5 class="mb-3">Jadwal Pemeriksaan</h5>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="examination_date" class="form-label">Tanggal Pemeriksaan <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="examination_date" name="examination_date" required>
                                            <div class="invalid-feedback">Tanggal pemeriksaan wajib dipilih</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="examination_time" class="form-label">Waktu Pemeriksaan <span class="text-danger">*</span></label>
                                            <select class="form-select" id="examination_time" name="examination_time" required>
                                                <option value="">Pilih waktu</option>
                                            </select>
                                            <div class="invalid-feedback">Waktu pemeriksaan wajib dipilih</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Catatan Tambahan</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                                  placeholder="Catatan khusus atau keluhan (opsional)"></textarea>
                                    </div>

                                    <!-- Registration Summary -->
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Ringkasan Pendaftaran</h6>
                                            <div id="registration-summary">
                                                <!-- Summary will be populated here -->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-secondary" onclick="prevStep()">
                                            <i class="bi bi-arrow-left"></i> Sebelumnya
                                        </button>
                                        <button type="submit" class="btn btn-success" id="submit-registration">
                                            <i class="bi bi-check-circle"></i> Daftar Sekarang
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Check Registration Section -->
    <section id="check-registration" class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card shadow">
                        <div class="card-header bg-info text-white">
                            <h3 class="card-title mb-0">
                                <i class="bi bi-search"></i>
                                Cek Status Pendaftaran
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="checkRegistrationForm">
                                <div class="mb-3">
                                    <label for="registration_number" class="form-label">Nomor Registrasi</label>
                                    <input type="text" class="form-control" id="registration_number" 
                                           name="registration_number" placeholder="Masukkan nomor registrasi" required>
                                </div>
                                <button type="submit" class="btn btn-info w-100">
                                    <i class="bi bi-search"></i> Cek Status
                                </button>
                            </form>
                            
                            <!-- Registration Status Result -->
                            <div id="registration-status" class="mt-4" style="display: none;">
                                <!-- Status will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Laboratorium Online</h5>
                    <p>Sistem pendaftaran online untuk kemudahan pemeriksaan laboratorium Anda.</p>
                </div>
                <div class="col-md-6">
                    <h5>Kontak</h5>
                    <p>
                        <i class="bi bi-telephone"></i> (*************<br>
                        <i class="bi bi-envelope"></i> <EMAIL><br>
                        <i class="bi bi-clock"></i> Senin - Sabtu: 07:00 - 16:00
                    </p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2024 Laboratorium Online. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Memproses...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/app.js"></script>
</body>
</html>
