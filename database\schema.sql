-- Database: lab_registration
CREATE DATABASE IF NOT EXISTS lab_registration;
USE lab_registration;

-- Tabel Admin
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'super_admin') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> <PERSON><PERSON>
CREATE TABLE examination_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Pasien
CREATE TABLE patients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nik VARCHAR(16) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    gender ENUM('L', 'P') NOT NULL,
    phone VARCHAR(15) NOT NULL,
    email VARCHAR(100),
    address TEXT NOT NULL,
    bpjs_number VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(15),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Pendaftaran
CREATE TABLE registrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_number VARCHAR(20) UNIQUE NOT NULL,
    patient_id INT NOT NULL,
    examination_date DATE NOT NULL,
    examination_time TIME NOT NULL,
    status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    total_price DECIMAL(10,2) DEFAULT 0,
    payment_status ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid',
    created_by_admin INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_admin) REFERENCES admins(id) ON DELETE SET NULL
);

-- Tabel Detail Pemeriksaan per Pendaftaran
CREATE TABLE registration_examinations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    examination_type_id INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE,
    FOREIGN KEY (examination_type_id) REFERENCES examination_types(id) ON DELETE CASCADE
);

-- Tabel Upload Dokumen
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    document_type ENUM('ktp', 'bpjs', 'rujukan', 'other') NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
);

-- Insert data admin default
INSERT INTO admins (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'super_admin');

-- Insert data jenis pemeriksaan default
INSERT INTO examination_types (code, name, description, price) VALUES 
('LAB001', 'Darah Lengkap', 'Pemeriksaan darah lengkap (Hb, Leukosit, Trombosit, dll)', 75000),
('LAB002', 'Urine Lengkap', 'Pemeriksaan urine lengkap', 50000),
('LAB003', 'Gula Darah Puasa', 'Pemeriksaan kadar gula darah puasa', 35000),
('LAB004', 'Kolesterol Total', 'Pemeriksaan kadar kolesterol total', 40000),
('LAB005', 'Asam Urat', 'Pemeriksaan kadar asam urat', 35000),
('LAB006', 'Fungsi Hati (SGOT/SGPT)', 'Pemeriksaan fungsi hati', 80000),
('LAB007', 'Fungsi Ginjal (Ureum/Kreatinin)', 'Pemeriksaan fungsi ginjal', 70000),
('LAB008', 'HbA1c', 'Pemeriksaan gula darah rata-rata 3 bulan', 120000),
('LAB009', 'Profil Lipid', 'Pemeriksaan kolesterol lengkap', 85000),
('LAB010', 'Hepatitis B Surface Antigen', 'Pemeriksaan HBsAg', 65000);

-- Tabel Log Aktivitas Admin
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
);

-- Create indexes untuk performa
CREATE INDEX idx_patients_nik ON patients(nik);
CREATE INDEX idx_registrations_number ON registrations(registration_number);
CREATE INDEX idx_registrations_date ON registrations(examination_date);
CREATE INDEX idx_registrations_status ON registrations(status);
CREATE INDEX idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX idx_admin_logs_created_at ON admin_logs(created_at);
