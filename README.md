# Sistem Pendaftaran Online Laboratorium

Sistem pendaftaran online untuk laboratorium kesehatan dengan fitur lengkap untuk pasien dan admin.

## 🚀 Fitur Utama

- **Frontend Modern**: React.js dengan Bootstrap untuk UI responsif
- **Backend Robust**: Node.js dengan Express.js
- **Database**: MySQL untuk penyimpanan data
- **Keamanan**: JWT authentication, input validation, CSRF protection
- **Fitur Tambahan**: PDF export, email notification, upload dokumen
- **Integrasi BPJS**: API integration untuk validasi data JKN

## 📋 Persyaratan Sistem

- Node.js (v14 atau lebih baru)
- MySQL (v8.0 atau lebih baru)
- XAMPP (untuk development lokal)

## 🛠️ Instalasi

1. Clone repository ini
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy `.env.example` ke `.env` dan sesuaikan konfigurasi
4. Import database schema dari `database/schema.sql`
5. Jalankan server:
   ```bash
   npm run dev
   ```

## 📁 Struktur Proyek

```
├── public/                 # Frontend files
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Images
│   └── index.html         # Main page
├── src/                   # Backend source
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Custom middleware
│   ├── models/           # Database models
│   └── routes/           # API routes
├── database/             # Database files
├── uploads/              # Uploaded files
└── server.js            # Main server file
```

## 🔧 Konfigurasi Database

Buat database MySQL dengan nama `lab_registration` dan import schema yang disediakan.

## 📱 Akses Sistem

- **Pasien**: http://localhost:3000
- **Admin**: http://localhost:3000/admin

## 🧪 Testing

Jalankan test dengan:
```bash
npm test
```

## 📄 Lisensi

MIT License
