# API Documentation - Sistem Pendaftaran Online Laboratorium

## Base URL
```
http://localhost:3000/api
```

## Authentication
Beberapa endpoint memerlukan authentication menggunakan JWT token.

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

---

## 🏥 Patients API

### Check NIK
Cek apakah NIK sudah terdaftar dalam sistem.

```http
GET /patients/check-nik/{nik}
```

**Response:**
```json
{
  "exists": true,
  "patient": {
    "id": 1,
    "full_name": "<PERSON>",
    "phone": "081234567890",
    "email": "<EMAIL>"
  }
}
```

### Register Patient
Mendaftarkan pasien baru.

```http
POST /patients/register
```

**Request Body:**
```json
{
  "nik": "1234567890123456",
  "full_name": "<PERSON>",
  "birth_date": "1990-01-15",
  "gender": "<PERSON>",
  "phone": "081234567890",
  "email": "<EMAIL>",
  "address": "Jl. Contoh No. 123, Jakarta",
  "bpjs_number": "1234567890123",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_phone": "081234567891"
}
```

### Get Patient
Ambil data pasien berdasarkan ID.

```http
GET /patients/{id}
```

### Search Patients
Cari pasien berdasarkan NIK atau nama.

```http
GET /patients/search/{query}
```

---

## 📅 Registrations API

### Create Registration
Buat registrasi pemeriksaan baru.

```http
POST /registrations
```

**Request Body:**
```json
{
  "patient_id": 1,
  "examination_date": "2024-01-20",
  "examination_time": "08:00",
  "examination_types": [1, 2, 3],
  "notes": "Catatan tambahan"
}
```

### Get Registration by Number
Ambil data registrasi berdasarkan nomor registrasi.

```http
GET /registrations/number/{registrationNumber}
```

### Upload Documents
Upload dokumen untuk registrasi.

```http
POST /registrations/{id}/documents
```

**Form Data:**
- `documents`: File(s) to upload
- `document_type`: ktp|bpjs|rujukan|other

### Get Available Slots
Ambil slot waktu yang tersedia untuk tanggal tertentu.

```http
GET /registrations/available-slots/{date}
```

**Response:**
```json
{
  "date": "2024-01-20",
  "available_slots": ["08:00", "08:30", "09:00"],
  "booked_slots": ["10:00", "10:30"]
}
```

---

## 🔬 Examinations API

### Get All Examinations
Ambil semua jenis pemeriksaan yang aktif.

```http
GET /examinations
```

### Get Examinations by Category
Ambil pemeriksaan berdasarkan kategori.

```http
GET /examinations/category/{category}
```

**Categories:** `darah`, `urine`, `fungsi-organ`, `infeksi`

### Get Examination Detail
Ambil detail pemeriksaan berdasarkan ID.

```http
GET /examinations/{id}
```

### Search Examinations
Cari pemeriksaan berdasarkan nama.

```http
GET /examinations/search/{query}
```

### Get Popular Packages
Ambil paket pemeriksaan populer.

```http
GET /examinations/packages/popular
```

---

## 👨‍💼 Admin API

### Admin Login
Login untuk admin.

```http
POST /admin/login
```

**Request Body:**
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**Response:**
```json
{
  "message": "Login berhasil",
  "token": "jwt_token_here",
  "admin": {
    "id": 1,
    "username": "admin",
    "full_name": "Administrator",
    "role": "super_admin"
  }
}
```

### Dashboard Data
Ambil data dashboard admin. **[Requires Auth]**

```http
GET /admin/dashboard
```

### Get All Registrations
Ambil semua registrasi dengan filter. **[Requires Auth]**

```http
GET /admin/registrations?page=1&limit=20&status=pending&date_from=2024-01-01&date_to=2024-01-31&search=john
```

### Get Registration Detail
Ambil detail registrasi untuk admin. **[Requires Auth]**

```http
GET /admin/registrations/{id}
```

### Update Registration Status
Update status registrasi. **[Requires Auth]**

```http
PUT /admin/registrations/{id}/status
```

**Request Body:**
```json
{
  "status": "confirmed",
  "notes": "Registrasi dikonfirmasi"
}
```

### Get Statistics Report
Ambil laporan statistik. **[Requires Auth]**

```http
GET /admin/reports/statistics?period=monthly&year=2024
```

---

## 📄 PDF API

### Generate Registration PDF
Generate PDF bukti registrasi.

```http
GET /api/pdf/registration/{registrationNumber}
```

### Generate Registration Report PDF
Generate PDF laporan registrasi. **[Requires Auth]**

```http
GET /api/pdf/report/registrations?status=completed&date_from=2024-01-01&date_to=2024-01-31
```

### Generate Result PDF
Generate PDF hasil pemeriksaan. **[Requires Auth]**

```http
GET /api/pdf/result/{registrationId}
```

---

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 409  | Conflict |
| 429  | Too Many Requests |
| 500  | Internal Server Error |

---

## 🔒 Rate Limiting

### API Endpoints
- **General API**: 100 requests per 15 minutes per IP
- **Login**: 5 attempts per 15 minutes per IP
- **Registration**: 10 registrations per hour per IP

### Headers
Rate limit information is included in response headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

---

## 📝 Error Response Format

```json
{
  "error": "Error message",
  "details": [
    {
      "field": "field_name",
      "message": "Validation error message",
      "value": "invalid_value"
    }
  ]
}
```

---

## 🧪 Example Usage

### JavaScript (Fetch API)
```javascript
// Register new patient
const registerPatient = async (patientData) => {
  try {
    const response = await fetch('/api/patients/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(patientData)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('Patient registered:', result.patient);
    } else {
      console.error('Registration failed:', result.error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};

// Admin login
const adminLogin = async (credentials) => {
  try {
    const response = await fetch('/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(credentials)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      localStorage.setItem('admin_token', result.token);
      console.log('Login successful');
    } else {
      console.error('Login failed:', result.error);
    }
  } catch (error) {
    console.error('Login error:', error);
  }
};

// Get dashboard data (with auth)
const getDashboardData = async () => {
  try {
    const token = localStorage.getItem('admin_token');
    const response = await fetch('/api/admin/dashboard', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Dashboard data:', data);
    } else {
      console.error('Failed to get dashboard data:', data.error);
    }
  } catch (error) {
    console.error('Dashboard error:', error);
  }
};
```

### cURL Examples
```bash
# Check NIK
curl -X GET "http://localhost:3000/api/patients/check-nik/1234567890123456"

# Register patient
curl -X POST "http://localhost:3000/api/patients/register" \
  -H "Content-Type: application/json" \
  -d '{
    "nik": "1234567890123456",
    "full_name": "John Doe",
    "birth_date": "1990-01-15",
    "gender": "L",
    "phone": "081234567890",
    "email": "<EMAIL>",
    "address": "Jl. Contoh No. 123, Jakarta"
  }'

# Admin login
curl -X POST "http://localhost:3000/api/admin/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'

# Get dashboard (with auth)
curl -X GET "http://localhost:3000/api/admin/dashboard" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 🔄 Webhooks (Future Enhancement)

Sistem dapat dikembangkan untuk mendukung webhooks untuk notifikasi real-time:

```http
POST /api/webhooks/registration-status
```

**Payload:**
```json
{
  "event": "registration.status_changed",
  "data": {
    "registration_id": 123,
    "old_status": "pending",
    "new_status": "confirmed",
    "timestamp": "2024-01-20T10:30:00Z"
  }
}
```

---

**Happy Coding! 🚀**
