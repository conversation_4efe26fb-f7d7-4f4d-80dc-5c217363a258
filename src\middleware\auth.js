const jwt = require('jsonwebtoken');
const { query } = require('../models/database');

// Middleware untuk verifikasi JWT token
const verifyToken = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        
        if (!token) {
            return res.status(401).json({ 
                error: 'Token akses diperlukan' 
            });
        }
        
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Verifikasi admin masih aktif di database
        const admin = await query(
            'SELECT id, username, email, full_name, role FROM admins WHERE id = ?',
            [decoded.id]
        );
        
        if (admin.length === 0) {
            return res.status(401).json({ 
                error: 'Token tidak valid' 
            });
        }
        
        req.admin = admin[0];
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ 
                error: 'Token tidak valid' 
            });
        }
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ 
                error: 'Token sudah kadaluarsa' 
            });
        }
        
        console.error('Auth middleware error:', error);
        res.status(500).json({ 
            error: 'Kesalahan server saat verifikasi token' 
        });
    }
};

// Middleware untuk verifikasi role admin
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.admin) {
            return res.status(401).json({ 
                error: 'Akses ditolak' 
            });
        }
        
        if (!roles.includes(req.admin.role)) {
            return res.status(403).json({ 
                error: 'Tidak memiliki izin untuk akses ini' 
            });
        }
        
        next();
    };
};

// Middleware untuk rate limiting per user
const userRateLimit = (maxRequests = 50, windowMs = 15 * 60 * 1000) => {
    const requests = new Map();
    
    return (req, res, next) => {
        const identifier = req.admin?.id || req.ip;
        const now = Date.now();
        
        if (!requests.has(identifier)) {
            requests.set(identifier, { count: 1, resetTime: now + windowMs });
            return next();
        }
        
        const userRequests = requests.get(identifier);
        
        if (now > userRequests.resetTime) {
            userRequests.count = 1;
            userRequests.resetTime = now + windowMs;
            return next();
        }
        
        if (userRequests.count >= maxRequests) {
            return res.status(429).json({
                error: 'Terlalu banyak permintaan, coba lagi nanti',
                retryAfter: Math.ceil((userRequests.resetTime - now) / 1000)
            });
        }
        
        userRequests.count++;
        next();
    };
};

// Middleware untuk logging aktivitas admin
const logActivity = (action) => {
    return async (req, res, next) => {
        const originalSend = res.send;
        
        res.send = function(data) {
            // Log aktivitas jika request berhasil
            if (res.statusCode < 400 && req.admin) {
                const logData = {
                    admin_id: req.admin.id,
                    action: action,
                    ip_address: req.ip,
                    user_agent: req.get('User-Agent'),
                    timestamp: new Date()
                };
                
                // Async logging tanpa menunggu
                query(
                    `INSERT INTO admin_logs (admin_id, action, ip_address, user_agent, created_at) 
                     VALUES (?, ?, ?, ?, ?)`,
                    [logData.admin_id, logData.action, logData.ip_address, logData.user_agent, logData.timestamp]
                ).catch(error => {
                    console.error('Failed to log admin activity:', error);
                });
            }
            
            originalSend.call(this, data);
        };
        
        next();
    };
};

module.exports = {
    verifyToken,
    requireRole,
    userRateLimit,
    logActivity
};
